<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>InfiniFlow 寶宸數位</title>
<script type="text/javascript" crossorigin="anonymous" async="" src="./assets/array.js"></script>
<script src="./assets/e.js"></script>
<link rel="preconnect" href="https://fonts.googleapis.com/">
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
<link href="./assets/css2" rel="stylesheet">
<link href="./assets/remixicon.min.css" rel="stylesheet">
<script src="./assets/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#8b5cf6',secondary:'#ec4899'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
@keyframes gradientFlow {
0% { background-position: 0% 50%; }
50% { background-position: 100% 50%; }
100% { background-position: 0% 50%; }
}
.gradient-bg {
background: linear-gradient(-45deg, #5727b0, #8b5cf6, #ec4899, #3b82f6);
background-size: 400% 400%;
animation: gradientFlow 15s ease infinite;
}
.gradient-text {
background: linear-gradient(90deg, #8b5cf6, #ec4899);
-webkit-background-clip: text;
background-clip: text;
color: transparent;
}
.card-hover {
transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.card-hover:hover {
transform: translateY(-5px);
box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
.btn-hover {
transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.btn-hover:hover {
transform: scale(1.05);
box-shadow: 0 10px 15px -3px rgba(139, 92, 246, 0.3), 0 4px 6px -2px rgba(139, 92, 246, 0.2);
}
input:focus {
outline: none;
}
<style>
@keyframes bounce {
0%, 100% { transform: translateY(0); }
50% { transform: translateY(-10px); }
}
@keyframes slideIn {
from { transform: scale(0.8); opacity: 0; }
to { transform: scale(1); opacity: 1; }
}
@keyframes fadeIn {
from { opacity: 0; }
to { opacity: 1; }
}
.chatbot-widget {
animation: bounce 3s ease-in-out infinite;
}
.chatbot-widget:hover {
animation: none;
transform: scale(1.1);
transition: transform 0.3s ease;
}
.chat-window {
animation: slideIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
transform-origin: bottom right;
}
.chat-message-bot {
animation: fadeIn 0.5s ease-out;
}
.chat-message-user {
animation: fadeIn 0.5s ease-out;
}
</style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{inset:0px}.-inset-4{inset:-1rem}.left-0{left:0px}.top-0{top:0px}.bottom-0{bottom:0px}.right-0{right:0px}.bottom-10{bottom:2.5rem}.left-\[10\%\]{left:10%}.left-\[55\%\]{left:55%}.right-6{right:1.5rem}.top-1\/2{top:50%}.z-0{z-index:0}.z-10{z-index:10}.z-20{z-index:20}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.mb-12{margin-bottom:3rem}.mb-16{margin-bottom:4rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.ml-3{margin-left:0.75rem}.mr-3{margin-right:0.75rem}.mt-12{margin-top:3rem}.mb-2{margin-bottom:0.5rem}.ml-2{margin-left:0.5rem}.mr-1{margin-right:0.25rem}.mr-2{margin-right:0.5rem}.mb-1{margin-bottom:0.25rem}.mb-24{margin-bottom:6rem}.mr-4{margin-right:1rem}.mt-1{margin-top:0.25rem}.-ml-8{margin-left:-2rem}.inline-block{display:inline-block}.flex{display:flex}.inline-flex{display:inline-flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-12{height:3rem}.h-8{height:2rem}.h-screen{height:100vh}.h-48{height:12rem}.h-14{height:3.5rem}.h-64{height:16rem}.h-full{height:100%}.h-\[72px\]{height:72px}.h-16{height:4rem}.h-\[400px\]{height:400px}.h-\[480px\]{height:480px}.h-auto{height:auto}.min-h-screen{min-height:100vh}.w-10{width:2.5rem}.w-12{width:3rem}.w-8{width:2rem}.w-full{width:100%}.w-14{width:3.5rem}.w-1\/2{width:50%}.w-16{width:4rem}.w-2\/3{width:66.666667%}.w-\[360px\]{width:360px}.w-\[40\%\]{width:40%}.max-w-2xl{max-width:42rem}.max-w-5xl{max-width:64rem}.max-w-6xl{max-width:72rem}.max-w-7xl{max-width:80rem}.max-w-xs{max-width:20rem}.max-w-3xl{max-width:48rem}.max-w-\[80\%\]{max-width:80%}.max-w-md{max-width:28rem}.flex-1{flex:1 1 0%}.flex-grow{flex-grow:1}.origin-bottom-right{transform-origin:bottom right}.-translate-y-1\/2{--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.scale-150{--tw-scale-x:1.5;--tw-scale-y:1.5;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:0.5rem}.gap-4{gap:1rem}.gap-8{gap:2rem}.gap-12{gap:3rem}.gap-16{gap:4rem}.gap-3{gap:0.75rem}.gap-6{gap:1.5rem}.-space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(-1rem * var(--tw-space-x-reverse));margin-left:calc(-1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-6 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.overflow-hidden{overflow:hidden}.overflow-y-auto{overflow-y:auto}.whitespace-nowrap{white-space:nowrap}.rounded-\[100px\]{border-radius:100px}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:16px}.rounded-xl{border-radius:20px}.rounded-2xl{border-radius:24px}.rounded-3xl{border-radius:32px}.rounded-tl-none{border-top-left-radius:0px}.border{border-width:1px}.border-2{border-width:2px}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.border-none{border-style:none}.border-white{--tw-border-opacity:1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}.border-white\/10{border-color:rgb(255 255 255 / 0.1)}.border-gray-800{--tw-border-opacity:1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.bg-gray-700{--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1))}.bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.bg-gray-900{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.bg-primary{--tw-bg-opacity:1;background-color:rgb(139 92 246 / var(--tw-bg-opacity, 1))}.bg-white\/10{background-color:rgb(255 255 255 / 0.1)}.bg-blue-500\/10{background-color:rgb(59 130 246 / 0.1)}.bg-blue-500\/20{background-color:rgb(59 130 246 / 0.2)}.bg-gray-950{--tw-bg-opacity:1;background-color:rgb(3 7 18 / var(--tw-bg-opacity, 1))}.bg-pink-500\/10{background-color:rgb(236 72 153 / 0.1)}.bg-pink-500\/20{background-color:rgb(236 72 153 / 0.2)}.bg-primary\/10{background-color:rgb(139 92 246 / 0.1)}.bg-primary\/20{background-color:rgb(139 92 246 / 0.2)}.bg-purple-500\/10{background-color:rgb(168 85 247 / 0.1)}.bg-purple-500\/20{background-color:rgb(168 85 247 / 0.2)}.bg-opacity-30{--tw-bg-opacity:0.3}.bg-opacity-50{--tw-bg-opacity:0.5}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.bg-gradient-to-b{background-image:linear-gradient(to bottom, var(--tw-gradient-stops))}.from-\[\#6366f1\]{--tw-gradient-from:#6366f1 var(--tw-gradient-from-position);--tw-gradient-to:rgb(99 102 241 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-primary\/80{--tw-gradient-from:rgb(139 92 246 / 0.8) var(--tw-gradient-from-position);--tw-gradient-to:rgb(139 92 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-blue-500{--tw-gradient-from:#3b82f6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(59 130 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-black\/60{--tw-gradient-from:rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);--tw-gradient-to:rgb(0 0 0 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-primary\/20{--tw-gradient-from:rgb(139 92 246 / 0.2) var(--tw-gradient-from-position);--tw-gradient-to:rgb(139 92 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-pink-300{--tw-gradient-from:#f9a8d4 var(--tw-gradient-from-position);--tw-gradient-to:rgb(249 168 212 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.via-black\/40{--tw-gradient-to:rgb(0 0 0 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgb(0 0 0 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to)}.via-purple-300{--tw-gradient-to:rgb(216 180 254 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #d8b4fe var(--tw-gradient-via-position), var(--tw-gradient-to)}.via-purple-500{--tw-gradient-to:rgb(168 85 247 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to)}.to-\[\#a855f7\]{--tw-gradient-to:#a855f7 var(--tw-gradient-to-position)}.to-secondary\/80{--tw-gradient-to:rgb(236 72 153 / 0.8) var(--tw-gradient-to-position)}.to-purple-500{--tw-gradient-to:#a855f7 var(--tw-gradient-to-position)}.to-gray-900{--tw-gradient-to:#111827 var(--tw-gradient-to-position)}.to-secondary\/20{--tw-gradient-to:rgb(236 72 153 / 0.2) var(--tw-gradient-to-position)}.to-blue-300{--tw-gradient-to:#93c5fd var(--tw-gradient-to-position)}.to-pink-500{--tw-gradient-to:#ec4899 var(--tw-gradient-to-position)}.object-cover{object-fit:cover}.object-top{object-position:top}.p-3{padding:0.75rem}.p-4{padding:1rem}.p-8{padding:2rem}.p-6{padding:1.5rem}.px-16{padding-left:4rem;padding-right:4rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.px-8{padding-left:2rem;padding-right:2rem}.py-2\.5{padding-top:0.625rem;padding-bottom:0.625rem}.py-20{padding-top:5rem;padding-bottom:5rem}.py-24{padding-top:6rem;padding-bottom:6rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-5{padding-top:1.25rem;padding-bottom:1.25rem}.px-12{padding-left:3rem;padding-right:3rem}.px-4{padding-left:1rem;padding-right:1rem}.py-16{padding-top:4rem;padding-bottom:4rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.pb-6{padding-bottom:1.5rem}.pt-8{padding-top:2rem}.text-left{text-align:left}.text-center{text-align:center}.font-\[\'Pacifico\'\]{font-family:'Pacifico'}.text-2xl{font-size:1.5rem;line-height:2rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-5xl{font-size:3rem;line-height:1}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.italic{font-style:italic}.leading-tight{line-height:1.25}.text-gray-200{--tw-text-opacity:1;color:rgb(229 231 235 / var(--tw-text-opacity, 1))}.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.text-primary{--tw-text-opacity:1;color:rgb(139 92 246 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-yellow-400{--tw-text-opacity:1;color:rgb(250 204 21 / var(--tw-text-opacity, 1))}.text-green-500{--tw-text-opacity:1;color:rgb(34 197 94 / var(--tw-text-opacity, 1))}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-pink-500{--tw-text-opacity:1;color:rgb(236 72 153 / var(--tw-text-opacity, 1))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.opacity-0{opacity:0}.shadow-\[0_0_20px_rgba\(139\2c 92\2c 246\2c 0\.3\)\]{--tw-shadow:0 0 20px rgba(139,92,246,0.3);--tw-shadow-colored:0 0 20px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-2xl{--tw-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-black\/5{--tw-shadow-color:rgb(0 0 0 / 0.05);--tw-shadow:var(--tw-shadow-colored)}.shadow-primary\/20{--tw-shadow-color:rgb(139 92 246 / 0.2);--tw-shadow:var(--tw-shadow-colored)}.blur-lg{--tw-blur:blur(16px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.brightness-75{--tw-brightness:brightness(.75);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.backdrop-blur-md{--tw-backdrop-blur:blur(12px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.duration-300{transition-duration:300ms}.duration-500{transition-duration:500ms}.after\:absolute::after{content:var(--tw-content);position:absolute}.after\:bottom-0::after{content:var(--tw-content);bottom:0px}.after\:left-0::after{content:var(--tw-content);left:0px}.after\:h-\[2px\]::after{content:var(--tw-content);height:2px}.after\:w-0::after{content:var(--tw-content);width:0px}.after\:bg-primary::after{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(139 92 246 / var(--tw-bg-opacity, 1))}.after\:transition-all::after{content:var(--tw-content);transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.after\:duration-300::after{content:var(--tw-content);transition-duration:300ms}.after\:content-\[\'\'\]::after{--tw-content:'';content:var(--tw-content)}.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:bg-gray-800:hover{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.hover\:bg-primary:hover{--tw-bg-opacity:1;background-color:rgb(139 92 246 / var(--tw-bg-opacity, 1))}.hover\:bg-primary\/90:hover{background-color:rgb(139 92 246 / 0.9)}.hover\:from-primary:hover{--tw-gradient-from:#8b5cf6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(139 92 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:to-secondary:hover{--tw-gradient-to:#ec4899 var(--tw-gradient-to-position)}.hover\:text-primary:hover{--tw-text-opacity:1;color:rgb(139 92 246 / var(--tw-text-opacity, 1))}.hover\:text-secondary:hover{--tw-text-opacity:1;color:rgb(236 72 153 / var(--tw-text-opacity, 1))}.hover\:text-white:hover{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.hover\:opacity-90:hover{opacity:0.9}.hover\:shadow-\[0_0_25px_rgba\(139\2c 92\2c 246\2c 0\.4\)\]:hover{--tw-shadow:0 0 25px rgba(139,92,246,0.4);--tw-shadow-colored:0 0 25px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.hover\:shadow-xl:hover{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.hover\:after\:w-full:hover::after{content:var(--tw-content);width:100%}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-primary\/50:focus{--tw-ring-color:rgb(139 92 246 / 0.5)}.group:hover .group-hover\:scale-105{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group:hover .group-hover\:from-black\/80{--tw-gradient-from:rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);--tw-gradient-to:rgb(0 0 0 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.group:hover .group-hover\:via-black\/60{--tw-gradient-to:rgb(0 0 0 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgb(0 0 0 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to)}.group:hover .group-hover\:to-gray-900{--tw-gradient-to:#111827 var(--tw-gradient-to-position)}.group:hover .group-hover\:opacity-100{opacity:1}@media (min-width: 640px){.sm\:flex-row{flex-direction:row}}@media (min-width: 768px){.md\:col-span-2{grid-column:span 2 / span 2}.md\:mb-0{margin-bottom:0px}.md\:inline-block{display:inline-block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:flex-1{flex:1 1 0%}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.md\:grid-cols-5{grid-template-columns:repeat(5, minmax(0, 1fr))}.md\:flex-row{flex-direction:row}.md\:text-2xl{font-size:1.5rem;line-height:2rem}.md\:text-7xl{font-size:4.5rem;line-height:1}}</style><link type="image/png" rel="icon" href="https://public.readdy.ai/gen_page/readdy-logo.png"><link rel="stylesheet" href="./assets/css2(1)"></head>
<body class="bg-black text-white min-h-screen relative">
<!-- Hero Section with Gradient Background -->
<div class="relative min-h-screen overflow-hidden">
<div class="absolute inset-0 z-0" style="background-image: url(&#39;https://readdy.ai/api/search-image?query=abstract%20digital%20fluid%20gradient%20wave%20with%20blue%2C%20purple%2C%20and%20magenta%20colors%20flowing%20like%20liquid%20on%20black%20background%2C%20elegant%20modern%20technology%20visualization%2C%20high%20resolution%2C%20no%20text%2C%20minimalist%20design&amp;width=1920&amp;height=1080&amp;seq=hero123&amp;orientation=landscape&#39;); background-size: cover; background-position: center;"></div>
<div class="absolute inset-0 bg-black bg-opacity-50 z-10"></div>
<!-- Navigation -->
<nav class="fixed w-full top-0 left-0 z-50 px-6 py-4">
<div class="max-w-6xl mx-auto">
<div class="bg-white/10 backdrop-blur-md rounded-full border border-white/10 shadow-lg shadow-black/5 px-6 py-3 flex items-center justify-between">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="flex items-center">
<span class="text-2xl font-[&#39;Pacifico&#39;] text-white">logo</span>
</a>
<div class="hidden md:flex-1 md:flex items-center justify-center space-x-8">
<div class="flex items-center space-x-8">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">Home</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">Features</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">Pricing</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">About</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">Blogs</a>
</div>
</div>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="hidden md:inline-block px-8 py-2.5 bg-gradient-to-r from-primary/80 to-secondary/80 hover:from-primary hover:to-secondary rounded-full text-white font-medium transition-all duration-300 shadow-[0_0_20px_rgba(139,92,246,0.3)] hover:shadow-[0_0_25px_rgba(139,92,246,0.4)] hover:scale-105">Get Started</a>
<button class="md:hidden w-10 h-10 flex items-center justify-center text-white">
<i class="ri-menu-line ri-lg"></i>
</button>
</div>
</div>
</nav>
<!-- Hero Content -->
<div class="relative z-20 flex flex-col items-center justify-center px-6 py-24 text-center h-screen">
<span class="text-primary font-medium mb-4">AI-POWERED MARKETING PLATFORM</span>
<h1 class="text-5xl md:text-7xl font-bold mb-8 max-w-5xl leading-tight">Transform Your Marketing with <span class="gradient-text">Intelligent Automation</span></h1>
<p class="text-xl md:text-2xl text-gray-200 mb-12 max-w-2xl">Scale your growth with AI-driven insights, personalized campaigns, and automated workflows that deliver results 10x faster.</p>
<div class="flex flex-col sm:flex-row items-center gap-4">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="btn-hover bg-gradient-to-r from-[#6366f1] to-[#a855f7] text-white text-xl font-medium px-16 py-5 rounded-[100px] shadow-lg hover:shadow-xl transition-all duration-300 whitespace-nowrap">Start Free Trial</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="flex items-center gap-2 text-xl font-medium hover:text-primary transition-colors">
<i class="ri-play-circle-line ri-xl"></i>
Watch Demo
</a>
</div>
<div class="flex items-center gap-8 mt-12">
<div class="flex -space-x-4">
<img src="./assets/search-image" alt="User Avatar" class="w-12 h-12 rounded-full border-2 border-white">
<img src="./assets/search-image(1)" alt="User Avatar" class="w-12 h-12 rounded-full border-2 border-white">
<img src="./assets/search-image(2)" alt="User Avatar" class="w-12 h-12 rounded-full border-2 border-white">
</div>
<div class="flex items-center gap-2">
<div class="flex items-center">
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
</div>
<p class="text-gray-300">Trusted by 10,000+ marketing teams</p>
</div>
</div>
</div>
</div>
<!-- Features Section -->
<section class="py-20 px-6 bg-black">
<div class="max-w-7xl mx-auto">
<div class="text-center mb-16">
<h2 class="text-primary text-xl mb-3">Our Features</h2>
<h3 class="text-4xl font-bold">Supercharge Your Productivity</h3>
</div>
<div class="grid md:grid-cols-2 gap-8">
<!-- Feature 1: Live Chat -->
<div class="bg-gray-900 rounded-xl p-8 card-hover">
<h4 class="text-2xl font-bold mb-4">Live Chat</h4>
<p class="text-gray-300 mb-6">Enhance customer experience with our 24/7 live chat feature. Get instant support, resolve queries quickly, and boost satisfaction with real-time assistance.</p>
<div class="bg-gray-800 rounded-lg p-4 mb-6">
<style>
@keyframes slideUp {
from {
opacity: 0;
transform: translateY(20px);
}
to {
opacity: 1;
transform: translateY(0);
}
}
.chat-message {
opacity: 0;
}
.chat-message.animate {
animation: slideUp 0.5s ease forwards;
}
.chat-message:nth-child(2) {
animation-delay: 0.2s;
}
</style>
<div class="chat-message flex items-start mb-4">
<div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center mr-3">
<i class="ri-user-line ri-sm"></i>
</div>
<div class="bg-gray-700 rounded-lg p-3 max-w-xs">
<p class="text-sm">Hey! Could everything is going well?</p>
<span class="text-xs text-gray-400">10:45 AM</span>
</div>
</div>
<div class="chat-message flex items-start justify-end mb-4">
<div class="bg-primary bg-opacity-30 rounded-lg p-3 max-w-xs">
<p class="text-sm">Yes, all good! How may I assist you today?</p>
<span class="text-xs text-gray-400">10:46 AM</span>
</div>
<div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center ml-3">
<i class="ri-customer-service-line ri-sm"></i>
</div>
</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
entry.target.classList.add('animate');
}
});
}, { threshold: 0.5 });
document.querySelectorAll('.chat-message').forEach(message => {
observer.observe(message);
});
});
</script>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-flex items-center text-primary hover:text-secondary transition-colors">
Explore Feature
<i class="ri-arrow-right-line ri-sm ml-2"></i>
</a>
</div>
<!-- Feature 2: Secured Payment -->
<div class="bg-gray-900 rounded-xl p-8 card-hover">
<h4 class="text-2xl font-bold mb-4">Secured Payment</h4>
<p class="text-gray-300 mb-6">Fast and secure payment with our advanced payment feature. Safeguard transactions with advanced encryption, protecting your financial data and providing a safe, reliable payment experience.</p>
<div class="bg-gray-800 rounded-lg p-4 mb-6">
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center mr-3">
<i class="ri-secure-payment-line ri-lg"></i>
</div>
<div>
<h5 class="font-medium">Payment Received</h5>
<div class="flex items-center">
<i class="ri-check-line ri-sm text-green-500 mr-1"></i>
<span class="text-sm text-green-500">Verified</span>
</div>
</div>
</div>
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-more-2-fill ri-lg"></i>
</div>
</div>
<div class="bg-gray-700 rounded-lg p-3">
<div class="flex items-center justify-between mb-2">
<span class="text-sm text-gray-300">Amount</span>
<span class="font-medium">$1,489.00</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-300">Date</span>
<span class="text-sm">May 27, 2025</span>
</div>
</div>
</div>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-flex items-center text-primary hover:text-secondary transition-colors">
Explore Feature
<i class="ri-arrow-right-line ri-sm ml-2"></i>
</a>
</div>
<!-- Feature 3: Receive Feedbacks -->
<div class="bg-gray-900 rounded-xl p-8 card-hover">
<h4 class="text-2xl font-bold mb-4">Receive Feedbacks</h4>
<p class="text-gray-300 mb-6">Improve your service with our feedback feature. Collect real-time customer insights, identify areas for improvement, and enhance satisfaction by listening to what your users have to say.</p>
<div class="bg-gray-800 rounded-lg p-4 mb-6">
<div class="flex items-center mb-3">
<i class="ri-double-quotes-l ri-2x text-primary mr-2"></i>
</div>
<p class="text-sm italic mb-3">"The marketing automation tools have completely transformed our workflow. We're saving at least 15 hours per week and seeing a 34% increase in engagement rates."</p>
<div class="flex items-center">
<div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center mr-3">
<i class="ri-user-line ri-sm"></i>
</div>
<div>
<p class="text-sm font-medium">Alexandra Reynolds</p>
<p class="text-xs text-gray-400">Marketing Director, TechVision</p>
</div>
</div>
</div>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-flex items-center text-primary hover:text-secondary transition-colors">
Explore Feature
<i class="ri-arrow-right-line ri-sm ml-2"></i>
</a>
</div>
<!-- Feature 4: Analytics Dashboard -->
<div class="bg-gray-900 rounded-xl p-8 card-hover">
<h4 class="text-2xl font-bold mb-4">Analytics Dashboard</h4>
<p class="text-gray-300 mb-6">Gain powerful insights with our comprehensive analytics dashboard. Track performance metrics, visualize data trends, and make data-driven decisions to optimize your marketing strategies.</p>
<div class="bg-gray-800 rounded-lg p-4 mb-6 h-48" id="analytics-chart" _echarts_instance_="ec_1752157061594" style="user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); position: relative;"><div style="position: relative; width: 359px; height: 160px; padding: 0px; margin: 0px; border-width: 0px;"><canvas data-zr-dom-id="zr_0" width="1373" height="611" style="position: absolute; left: 0px; top: 0px; width: 359px; height: 160px; user-select: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); padding: 0px; margin: 0px; border-width: 0px;"></canvas></div><div class=""></div></div>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-flex items-center text-primary hover:text-secondary transition-colors">
Explore Feature
<i class="ri-arrow-right-line ri-sm ml-2"></i>
</a>
</div>
</div>
</div>
</section>
<script type="text/javascript" crossorigin="anonymous" src="./assets/surveys.js"></script><script type="text/javascript" crossorigin="anonymous" src="./assets/dead-clicks-autocapture.js"></script><script type="text/javascript" crossorigin="anonymous" src="./assets/config.js"></script><script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>
<!-- Use Cases Section -->
<section class="py-20 px-6 bg-gray-950" id="use-cases-sec">
<div class="max-w-7xl mx-auto">
<div class="text-center mb-16">
<h2 class="text-primary text-xl mb-3">Use Cases</h2>
<h3 class="text-4xl font-bold mb-6">See How Businesses Succeed</h3>
<p class="text-gray-300 max-w-2xl mx-auto">Discover how companies across industries leverage our platform to achieve remarkable results and drive sustainable growth.</p>
</div>
<!-- 頁籤切換按鈕 -->
<div class="flex flex-wrap justify-center gap-3 mb-12">
<button class="tab-btn active px-6 py-3 rounded-full bg-primary text-white font-medium transition-all duration-300 hover:bg-primary/80" data-tab="ecommerce">
<i class="ri-shopping-cart-line mr-2"></i>電商與零售業
</button>
<button class="tab-btn px-6 py-3 rounded-full bg-gray-800 text-gray-300 font-medium transition-all duration-300 hover:bg-gray-700" data-tab="realestate">
<i class="ri-building-line mr-2"></i>房地產與仲介業
</button>
<button class="tab-btn px-6 py-3 rounded-full bg-gray-800 text-gray-300 font-medium transition-all duration-300 hover:bg-gray-700" data-tab="education">
<i class="ri-book-line mr-2"></i>教育顧問與知識付費
</button>
<button class="tab-btn px-6 py-3 rounded-full bg-gray-800 text-gray-300 font-medium transition-all duration-300 hover:bg-gray-700" data-tab="saas">
<i class="ri-cloud-line mr-2"></i>SaaS與軟體服務業
</button>
<button class="tab-btn px-6 py-3 rounded-full bg-gray-800 text-gray-300 font-medium transition-all duration-300 hover:bg-gray-700" data-tab="beauty">
<i class="ri-heart-pulse-line mr-2"></i>醫美 & 美業
</button>
<button class="tab-btn px-6 py-3 rounded-full bg-gray-800 text-gray-300 font-medium transition-all duration-300 hover:bg-gray-700" data-tab="creator">
<i class="ri-video-line mr-2"></i>內容創作者經濟
</button>
</div>

<!-- 頁籤內容區域 -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-16 mb-24" id="use-cases-content">
<!-- 內容將由JavaScript動態生成 -->
</div>
</div>
</section>
<script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>






<!-- Testimonials Section -->
<section class="py-20 px-6 bg-black">
<div class="max-w-7xl mx-auto">
<div class="text-center mb-16">
<h2 class="text-primary text-xl mb-3">Testimonials</h2>
<h3 class="text-4xl font-bold mb-6">What Our Clients Say</h3>
<p class="text-gray-300 max-w-2xl mx-auto">Hear from marketing professionals who have transformed their strategies with our platform.</p>
</div>
<div class="grid md:grid-cols-3 gap-12">
<!-- Testimonial 1 -->
<div class="bg-gray-900 rounded-xl p-8 card-hover flex flex-col">
<div class="flex items-center mb-6">
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
</div>
<p class="text-gray-300 italic flex-grow mb-6">"This platform has revolutionized our marketing operations. The automation features alone have saved us countless hours, and the analytics provide insights we never had access to before."</p>
<div class="flex items-center h-[72px]">
<div class="w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center mr-4">
<i class="ri-user-line ri-lg"></i>
</div>
<div>
<p class="font-medium">Michael Thompson</p>
<p class="text-sm text-gray-400">CMO, Elevate Solutions</p>
</div>
</div>
</div>
<!-- Testimonial 2 -->
<div class="bg-gray-900 rounded-xl p-8 card-hover flex flex-col">
<div class="flex items-center mb-6">
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
</div>
<p class="text-gray-300 italic flex-grow mb-6">"The ROI we've seen since implementing this platform has been incredible. Our campaign performance improved by 78% in just three months, and the customer support team is always there when we need them."</p>
<div class="flex items-center h-[72px]">
<div class="w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center mr-4">
<i class="ri-user-line ri-lg"></i>
</div>
<div>
<p class="font-medium">Samantha Wilson</p>
<p class="text-sm text-gray-400">Marketing Director, NexGen Retail</p>
</div>
</div>
</div>
<!-- Testimonial 3 -->
<div class="bg-gray-900 rounded-xl p-8 card-hover flex flex-col">
<div class="flex items-center mb-6">
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
</div>
<p class="text-gray-300 italic flex-grow mb-6">"As a small marketing team, we needed a solution that could help us compete with larger companies. This platform has leveled the playing field and allowed us to execute sophisticated campaigns that deliver real results."</p>
<div class="flex items-center h-[72px]">
<div class="w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center mr-4">
<i class="ri-user-line ri-lg"></i>
</div>
<div>
<p class="font-medium">David Rodriguez</p>
<p class="text-sm text-gray-400">Founder, Catalyst Marketing</p>
</div>
</div>
</div>
</div>
</div>
</section>
<script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>
<!-- Pricing Section -->
<!-- FAQ Section -->
<section class="py-20 px-6 bg-black">
<div class="max-w-3xl mx-auto">
<div class="text-center mb-16">
<h2 class="text-primary text-xl mb-3">FAQ</h2>
<h3 class="text-4xl font-bold mb-6">Frequently Asked Questions</h3>
<p class="text-gray-300">Find answers to common questions about our platform.</p>
</div>
<div class="space-y-4" id="faq-container">
<!-- FAQ Item 1 -->
<div class="border border-gray-800 rounded-lg overflow-hidden">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none">
<span class="text-lg font-medium">How does the 14-day free trial work?</span>
<i class="ri-add-line ri-lg faq-icon"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300">Our 14-day free trial gives you full access to all features of the Professional plan. No credit card is required to start, and you can cancel anytime. At the end of the trial, you can choose to upgrade to a paid plan or your account will automatically switch to our limited free plan.</p>
</div>
</div>
<!-- FAQ Item 2 -->
<div class="border border-gray-800 rounded-lg overflow-hidden">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none">
<span class="text-lg font-medium">Can I change plans later?</span>
<i class="ri-add-line ri-lg faq-icon"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300">Absolutely! You can upgrade, downgrade, or change your plan at any time. When upgrading, the new features will be immediately available. If you downgrade, the changes will take effect at the start of your next billing cycle.</p>
</div>
</div>
<!-- FAQ Item 3 -->
<div class="border border-gray-800 rounded-lg overflow-hidden">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none">
<span class="text-lg font-medium">Do you offer custom solutions?</span>
<i class="ri-add-line ri-lg faq-icon"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300">Yes, we offer custom solutions for businesses with specific needs. Our Enterprise plan includes custom integrations, and our team can work with you to develop tailored features. Contact our sales team to discuss your requirements and get a personalized quote.</p>
</div>
</div>
<!-- FAQ Item 4 -->
<div class="border border-gray-800 rounded-lg overflow-hidden">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none">
<span class="text-lg font-medium">What kind of support do you provide?</span>
<i class="ri-add-line ri-lg faq-icon"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300">We offer multiple support channels based on your plan. All customers receive email support with a 24-hour response time. Professional plans include live chat support during business hours, while Enterprise customers get 24/7 priority support and a dedicated account manager. We also maintain an extensive knowledge base and regular webinars.</p>
</div>
</div>
<!-- FAQ Item 5 -->
<div class="border border-gray-800 rounded-lg overflow-hidden">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none">
<span class="text-lg font-medium">Is my data secure?</span>
<i class="ri-add-line ri-lg faq-icon"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300">Security is our top priority. We use industry-standard encryption for all data, both in transit and at rest. Our platform is SOC 2 compliant and GDPR ready. We perform regular security audits and penetration testing to ensure your data remains protected. You can review our security practices in detail in our security whitepaper.</p>
</div>
</div>
</div>
</div>
</section>
<script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>
<!-- CTA Section -->
<section class="relative h-[400px] overflow-hidden rounded-3xl mx-auto max-w-7xl">
<div class="absolute inset-0 bg-gradient-to-r from-pink-300 via-purple-300 to-blue-300"></div>
<style>
@keyframes rotateClockwise {
0% {
transform: rotate(0deg);
}
100% {
transform: rotate(360deg);
}
}
@keyframes rotateCounterClockwise {
0% {
transform: rotate(0deg);
}
100% {
transform: rotate(-360deg);
}
}
</style>
<div class="absolute left-[10%] top-1/2 -translate-y-1/2 w-[40%] perspective-[1000px] flex items-center gap-4">
<div class="w-2/3 relative z-0">
<img src="./assets/98396f56-37d0-4c87-ace9-f7de22e11642.jpg" alt="Abstract Shape" class="w-full h-auto object-cover scale-150" style="animation: rotateClockwise 20s linear infinite;">
</div>
<div class="w-1/2 relative z-10 -ml-8">
<img src="./assets/3cf1d9a4-5823-403b-b9da-7dcf837b7aea.jpg" alt="Abstract Shape 2" class="w-full h-auto object-cover" style="animation: rotateCounterClockwise 20s linear infinite;">
</div>
</div>
<div class="absolute left-[55%] top-1/2 -translate-y-1/2 text-left">
<h2 class="text-5xl font-bold text-gray-900 mb-8">Unlock unlimited<br>growth.</h2>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-flex items-center bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-medium px-12 py-4 rounded-[100px] hover:opacity-90 transition-opacity whitespace-nowrap text-xl">
Get Started
</a>
</div>
</section>
<!-- Footer -->
<footer class="bg-gray-950 py-16 px-6">
<div class="max-w-7xl mx-auto">
<div class="grid grid-cols-1 md:grid-cols-5 gap-8 mb-12">
<div class="md:col-span-2">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-block mb-6">
<span class="text-3xl font-[&#39;Pacifico&#39;] text-white">logo</span>
</a>
<p class="text-gray-400 mb-6 max-w-md">Powerful marketing automation platform that helps businesses grow through intelligent campaigns, analytics, and personalized customer experiences.</p>
<div class="flex space-x-4">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-twitter-x-line ri-lg"></i>
</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-linkedin-line ri-lg"></i>
</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-facebook-line ri-lg"></i>
</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-instagram-line ri-lg"></i>
</a>
</div>
</div>
<div>
<h5 class="font-bold mb-6">Product</h5>
<ul class="space-y-3">
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Features</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Pricing</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Integrations</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Updates</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Roadmap</a></li>
</ul>
</div>
<div>
<h5 class="font-bold mb-6">Company</h5>
<ul class="space-y-3">
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">About Us</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Careers</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Blog</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Press</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Contact</a></li>
</ul>
</div>
<div>
<h5 class="font-bold mb-6">Resources</h5>
<ul class="space-y-3">
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Documentation</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Help Center</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Community</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Webinars</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Partners</a></li>
</ul>
</div>
</div>
<div class="pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
<p class="text-gray-500 mb-4 md:mb-0">© 2025 Marketing Platform. All rights reserved.</p>
<div class="flex flex-wrap gap-4">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-500 hover:text-primary transition-colors">Terms of Service</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-500 hover:text-primary transition-colors">Privacy Policy</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-500 hover:text-primary transition-colors">Cookie Policy</a>
</div>
</div>
</div>
</footer>
<!-- Chatbot Widget -->
<div id="chatbot-container" class="fixed bottom-10 right-6 z-50">
<!-- Chat Window -->
<div id="chat-window" class="hidden chat-window bg-gray-900 w-[360px] h-[480px] rounded-2xl shadow-2xl flex flex-col mb-4 border border-gray-800 origin-bottom-right">
<!-- Chat Header -->
<div class="p-4 border-b border-gray-800 flex items-center justify-between">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3">
<i class="ri-customer-service-2-line ri-lg text-primary"></i>
</div>
<div>
<h4 class="font-medium">Chat Support</h4>
<p class="text-sm text-gray-400">Online</p>
</div>
</div>
<button id="close-chat" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-800 transition-colors">
<i class="ri-close-line ri-lg"></i>
</button>
</div>
<!-- Chat Messages -->
<div id="chat-messages" class="flex-1 overflow-y-auto p-4 space-y-4">
<div class="chat-message-bot flex items-start">
<div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3">
<i class="ri-customer-service-2-line text-primary"></i>
</div>
<div class="bg-gray-800 rounded-lg rounded-tl-none p-3 max-w-[80%]">
<p class="text-sm">Hello! 👋 How can I help you today?</p>
</div>
</div>
</div>
<!-- Chat Input -->
<div class="p-4 border-t border-gray-800">
<div class="flex items-center gap-2">
<input type="text" id="chat-input" placeholder="Type your message..." class="flex-1 bg-gray-800 rounded-full px-4 py-2 text-sm focus:ring-2 focus:ring-primary/50 border-none">
<button id="send-message" class="w-10 h-10 rounded-full bg-primary flex items-center justify-center hover:bg-primary/90 transition-colors">
<i class="ri-send-plane-fill ri-sm"></i>
</button>
</div>
</div>
</div>
<!-- Chatbot Button -->
<button id="chatbot-button" class="chatbot-widget w-16 h-16 rounded-full bg-primary flex items-center justify-center shadow-lg hover:shadow-xl">
<i class="ri-customer-service-2-line ri-2x text-white"></i>
</button>
</div>
<script src="./assets/echarts.min.js"></script>
<script id="chart-script">
document.addEventListener('DOMContentLoaded', function() {
const chartContainer = document.getElementById('analytics-chart');
if (chartContainer) {
const chart = echarts.init(chartContainer);
const option = {
animation: false,
tooltip: {
trigger: 'axis',
backgroundColor: 'rgba(255, 255, 255, 0.8)',
textStyle: {
color: '#1f2937'
}
},
grid: {
top: 10,
right: 10,
bottom: 20,
left: 40
},
xAxis: {
type: 'category',
data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
axisLine: {
lineStyle: {
color: '#4b5563'
}
},
axisLabel: {
color: '#9ca3af'
}
},
yAxis: {
type: 'value',
axisLine: {
lineStyle: {
color: '#4b5563'
}
},
splitLine: {
lineStyle: {
color: '#374151'
}
},
axisLabel: {
color: '#9ca3af'
}
},
series: [
{
name: 'Engagement',
type: 'line',
smooth: true,
data: [120, 132, 101, 134, 90, 180],
lineStyle: {
color: 'rgba(87, 181, 231, 1)'
},
itemStyle: {
color: 'rgba(87, 181, 231, 1)'
},
showSymbol: false,
areaStyle: {
color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
{
offset: 0,
color: 'rgba(87, 181, 231, 0.3)'
},
{
offset: 1,
color: 'rgba(87, 181, 231, 0.1)'
}
])
}
},
{
name: 'Conversions',
type: 'line',
smooth: true,
data: [220, 182, 191, 234, 290, 330],
lineStyle: {
color: 'rgba(141, 211, 199, 1)'
},
itemStyle: {
color: 'rgba(141, 211, 199, 1)'
},
showSymbol: false,
areaStyle: {
color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
{
offset: 0,
color: 'rgba(141, 211, 199, 0.3)'
},
{
offset: 1,
color: 'rgba(141, 211, 199, 0.1)'
}
])
}
}
]
};
chart.setOption(option);
window.addEventListener('resize', function() {
chart.resize();
});
}
});
</script>
<script id="faq-script">
document.addEventListener('DOMContentLoaded', function() {
const faqButtons = document.querySelectorAll('.faq-button');
faqButtons.forEach(button => {
button.addEventListener('click', function() {
const content = this.nextElementSibling;
const icon = this.querySelector('.faq-icon');
if (content.classList.contains('hidden')) {
content.classList.remove('hidden');
icon.classList.remove('ri-add-line');
icon.classList.add('ri-subtract-line');
} else {
content.classList.add('hidden');
icon.classList.remove('ri-subtract-line');
icon.classList.add('ri-add-line');
}
});
});
});
</script>

<!-- 頁籤切換功能 -->
<script id="tab-switch-script">
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const contentArea = document.getElementById('use-cases-content');

    // 產業內容數據
    const industryData = {
        ecommerce: {
            title: '電商與零售業',
            subtitle: '智慧驅動，營收倍增',
            icon: 'ri-shopping-cart-line',
            scenarios: [
                {
                    title: '全自動「節慶活動」生成器',
                    painPoint: '為了母親節、雙11，行銷團隊提前一個月加班準備活動頁。',
                    solution: '只需在表單輸入「活動主題：母親節」、「主打商品：保養品」，系統自動生成充滿節日氛圍的登陸頁、EDM、社群貼文素材。',
                    benefit: '行銷活動準備時間縮短90%，讓團隊能同時操作多個活動，抓住每個商機。'
                },
                {
                    title: '千人千面的「商品故事頁」',
                    painPoint: '所有商品共用一個呆板的介紹模板。',
                    solution: '當您在後台新增商品時，AI會根據商品屬性自動生成獨一無二的銷售文案、使用情境和品牌故事，並填入專屬頁面。',
                    benefit: '提升商品頁轉換率25%+，創造更強的品牌情感連結。'
                }
            ],
            image: './assets/search-image'
        },
        realestate: {
            title: '房地產與仲介業',
            subtitle: '專業呈現，加速成交',
            icon: 'ri-building-line',
            scenarios: [
                {
                    title: '每個物件的「專屬金牌銷售網站」',
                    painPoint: '物件資訊僅僅是幾張照片和制式化的文字列表。',
                    solution: '仲介在後台App上傳新建案的照片和基本資料，InfiniFlow™ 自動為該物件生成一個獨立的精美介紹網站，包含AI生成的周邊生活圈分析、格局優點描述等。',
                    benefit: '提升物件的專業感與吸引力，增加約看率40%+。'
                }
            ],
            image: './assets/search-image(1)'
        },
        education: {
            title: '教育、顧問與知識付費產業',
            subtitle: '知識變現，毫不費力',
            icon: 'ri-book-line',
            scenarios: [
                {
                    title: '源源不絕的「課程與講座」銷售頁',
                    painPoint: '講師想開新課，但被製作銷售頁面的技術問題卡住。',
                    solution: '講師只需填寫一份課程大綱問卷，系統自動生成具備高度說服力的課程銷售頁。',
                    benefit: '新課程上架速度提升5倍，讓專家能專注於內容開發。'
                }
            ],
            image: './assets/search-image(2)'
        },
        saas: {
            title: 'SaaS與軟體服務業',
            subtitle: '資訊同步，價值加速',
            icon: 'ri-cloud-line',
            scenarios: [
                {
                    title: '新功能發布的「即時說明文件與案例」',
                    painPoint: '產品更新後，說明文件(Docs)和行銷部落格(Blog)總是不同步。',
                    solution: '開發團隊在Jira中完成一個新功能的註記，系統自動抓取資訊，生成給使用者的說明文件和給潛在客戶看的應用案例。',
                    benefit: '加速產品價值傳遞，確保行銷內容與產品功能永遠同步。'
                }
            ],
            image: './assets/search-image(3)'
        },
        beauty: {
            title: '醫美 & 美業',
            subtitle: '創造渴望，加速預約',
            icon: 'ri-heart-pulse-line',
            scenarios: [
                {
                    title: '「見證案例」自動生成與發布系統',
                    painPoint: '累積了大量成功的素人改造案例，但行銷人員沒時間一一撰寫成有吸引力的分享文，導致最有力的行銷資產被浪費。',
                    solution: '您的顧問只需在平板上傳客戶的「前後對比照」，並用勾選方式標記療程（如：皮秒雷射、玻尿酸填充）和客戶主要改善（如：膚色不均、法令紋）。InfiniFlow™ 會自動生成一篇符合醫療廣告法規、語氣專業又溫暖的「見證故事」網頁，並同步發布到官網和預約平台。',
                    benefit: '新案例上架時間縮短95%，讓官網永遠充滿最新、最真實的成功案例，大幅提升線上諮詢預約率達50%+。'
                },
                {
                    title: '個人化「美麗方案」智慧推薦引擎',
                    painPoint: '客戶對療程一知半解，諮詢師需要花大量時間重複解說基礎知識。',
                    solution: '在官網設置一個「我的美麗煩惱」互動問卷（勾選：毛孔粗大、鬆弛、想瘦小腹）。客戶完成後，InfiniFlow™ 會即時生成一個專屬的「個人化建議方案」頁面，結合AI生成的衛教知識、推薦療程組合、預算範圍和相關案例連結。',
                    benefit: '篩選出高意向客戶，有效縮短諮詢師溝通時間30%，讓客戶在到店前就已建立信任感與基本認知，提升當日成交率。'
                }
            ],
            image: './assets/search-image(4)'
        },
        creator: {
            title: '內容創作者經濟',
            subtitle: '內容放大，專注創作',
            icon: 'ri-video-line',
            scenarios: [
                {
                    title: '個人品牌「內容中樞」自動化',
                    painPoint: '創作者在 YouTube、Instagram、TikTok 各平台發布內容，但缺乏一個統一的、能將流量變現的「個人官網」。手動更新所有平台的內容到官網上，費時費力。',
                    solution: '創作者只需專注在自己最擅長的平台（如YouTube）發布內容。InfiniFlow™ 會自動監測，一旦有新影片，立刻：1) 將影片嵌入官網；2) 將影片內容轉為SEO文章；3) 擷取精華片段製作成短影音發布到其他平台；4) 自動更新官網上的「最新作品」列表。',
                    benefit: '實現「一次創作，全網自動分發」，輕鬆打造專業的個人品牌網站，將粉絲沉澱為自己的流量資產，為開設線上課程、電商帶貨鋪路。'
                },
                {
                    title: '粉絲贊助與付費內容「無人商店」',
                    painPoint: '想推出付費電子報、限定圖文或教學影片，但被建立會員系統、金流、內容發送等繁瑣技術流程卡住。',
                    solution: '創作者在一個簡單的表單中上傳他的付費內容（例如一份PDF或一段影片連結）和定價。InfiniFlow™ 自動為這個產品生成一個精美的銷售頁，並處理好金流串接。當有粉絲購買後，系統會自動將內容透過Email發送給粉絲。',
                    benefit: '讓任何創作者都能在10分鐘內擁有自己的付費產品，實現知識變現，創造被動收入，無需任何技術背景。'
                }
            ],
            image: './assets/search-image(5)'
        }
    };

    // 生成內容的函數
    function generateContent(industry) {
        const data = industryData[industry];
        if (!data) return '';

        let scenariosHtml = '';
        data.scenarios.forEach((scenario, index) => {
            scenariosHtml += `
                <div class="mb-8 ${index > 0 ? 'pt-8 border-t border-gray-700' : ''}">
                    <h5 class="text-xl font-bold mb-4 text-primary">${scenario.title}</h5>
                    <div class="space-y-4">
                        <div class="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
                            <h6 class="font-semibold text-red-400 mb-2">傳統痛點：</h6>
                            <p class="text-gray-300">${scenario.painPoint}</p>
                        </div>
                        <div class="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                            <h6 class="font-semibold text-blue-400 mb-2">InfiniFlow™ 應用：</h6>
                            <p class="text-gray-300">${scenario.solution}</p>
                        </div>
                        <div class="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                            <h6 class="font-semibold text-green-400 mb-2">實現效益：</h6>
                            <p class="text-gray-300">${scenario.benefit}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        return `
            <div class="bg-gray-900 rounded-xl p-8 relative group">
                <div class="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-lg opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                <div class="relative">
                    <div class="flex items-center mb-8">
                        <div class="w-14 h-14 rounded-full bg-primary/20 flex items-center justify-center mr-4 shadow-lg shadow-primary/20">
                            <i class="${data.icon} ri-2x text-primary"></i>
                        </div>
                        <div>
                            <h4 class="text-2xl font-bold">${data.title}</h4>
                            <span class="gradient-text text-lg font-medium">${data.subtitle}</span>
                        </div>
                    </div>
                    ${scenariosHtml}
                </div>
            </div>
            <div class="bg-gray-900 rounded-xl p-8 relative group flex items-center justify-center">
                <div class="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-lg opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                <div class="relative">
                    <img src="${data.image}" alt="${data.title}" class="w-full h-auto rounded-lg shadow-lg">
                </div>
            </div>
        `;
    }

    // 切換頁籤函數
    function switchTab(targetTab) {
        // 更新按鈕狀態
        tabButtons.forEach(btn => {
            if (btn.dataset.tab === targetTab) {
                btn.classList.remove('bg-gray-800', 'text-gray-300');
                btn.classList.add('bg-primary', 'text-white', 'active');
            } else {
                btn.classList.remove('bg-primary', 'text-white', 'active');
                btn.classList.add('bg-gray-800', 'text-gray-300');
            }
        });

        // 更新內容
        contentArea.innerHTML = generateContent(targetTab);
    }

    // 綁定點擊事件
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            switchTab(targetTab);
        });
    });

    // 初始化顯示第一個頁籤
    switchTab('ecommerce');
});
</script>

<!-- 聊天機器人 -->
<script id="chatbot-script">
document.addEventListener('DOMContentLoaded', function() {
const chatbotButton = document.getElementById('chatbot-button');
const chatWindow = document.getElementById('chat-window');
const closeChat = document.getElementById('close-chat');
const chatInput = document.getElementById('chat-input');
const sendMessage = document.getElementById('send-message');
const chatMessages = document.getElementById('chat-messages');
function addMessage(message, isUser = false) {
const messageDiv = document.createElement('div');
messageDiv.className = isUser ? 'chat-message-user flex items-start justify-end' : 'chat-message-bot flex items-start';
const messageContent = `
${isUser ? '' : `
<div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3">
<i class="ri-customer-service-2-line text-primary"></i>
</div>
`}
<div class="bg-${isUser ? 'primary' : 'gray-800'} rounded-lg ${isUser ? 'rounded-tr-none' : 'rounded-tl-none'} p-3 max-w-[80%]">
<p class="text-sm">${message}</p>
</div>
`;
messageDiv.innerHTML = messageContent;
chatMessages.appendChild(messageDiv);
chatMessages.scrollTop = chatMessages.scrollHeight;
}
chatbotButton.addEventListener('click', function() {
chatWindow.classList.remove('hidden');
chatbotButton.classList.add('hidden');
if (!chatWindow.classList.contains('hidden')) {
chatInput.focus();
}
});
closeChat.addEventListener('click', function() {
chatWindow.classList.add('hidden');
chatbotButton.classList.remove('hidden');
});
function handleSendMessage() {
const message = chatInput.value.trim();
if (message) {
addMessage(message, true);
chatInput.value = '';
// Simulate bot response
setTimeout(() => {
addMessage("Thanks for your message! Our team will get back to you soon.");
}, 1000);
}
}
sendMessage.addEventListener('click', handleSendMessage);
chatInput.addEventListener('keypress', function(e) {
if (e.key === 'Enter') {
handleSendMessage();
}
});
});
</script>

</body></html>