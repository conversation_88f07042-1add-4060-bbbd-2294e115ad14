<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>InfiniFlow 寶宸數位</title>
<script type="text/javascript" crossorigin="anonymous" async="" src="./assets/array.js"></script>
<script src="./assets/e.js"></script>
<link rel="preconnect" href="https://fonts.googleapis.com/">
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
<link href="./assets/css2" rel="stylesheet">
<link href="./assets/remixicon.min.css" rel="stylesheet">
<script src="./assets/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#8b5cf6',secondary:'#ec4899'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
@keyframes gradientFlow {
0% { background-position: 0% 50%; }
50% { background-position: 100% 50%; }
100% { background-position: 0% 50%; }
}
.gradient-bg {
background: linear-gradient(-45deg, #5727b0, #8b5cf6, #ec4899, #3b82f6);
background-size: 400% 400%;
animation: gradientFlow 15s ease infinite;
}
.gradient-text {
background: linear-gradient(90deg, #8b5cf6, #ec4899);
-webkit-background-clip: text;
background-clip: text;
color: transparent;
}
.card-hover {
transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.card-hover:hover {
transform: translateY(-5px);
box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
.btn-hover {
transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.btn-hover:hover {
transform: scale(1.05);
box-shadow: 0 10px 15px -3px rgba(139, 92, 246, 0.3), 0 4px 6px -2px rgba(139, 92, 246, 0.2);
}
input:focus {
outline: none;
}
<style>
@keyframes bounce {
0%, 100% { transform: translateY(0); }
50% { transform: translateY(-10px); }
}
@keyframes slideIn {
from { transform: scale(0.8); opacity: 0; }
to { transform: scale(1); opacity: 1; }
}
@keyframes fadeIn {
from { opacity: 0; }
to { opacity: 1; }
}
.chatbot-widget {
animation: bounce 3s ease-in-out infinite;
}
.chatbot-widget:hover {
animation: none;
transform: scale(1.1);
transition: transform 0.3s ease;
}
.chat-window {
animation: slideIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
transform-origin: bottom right;
}
.chat-message-bot {
animation: fadeIn 0.5s ease-out;
}
.chat-message-user {
animation: fadeIn 0.5s ease-out;
}
</style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{inset:0px}.-inset-4{inset:-1rem}.left-0{left:0px}.top-0{top:0px}.bottom-0{bottom:0px}.right-0{right:0px}.bottom-10{bottom:2.5rem}.left-\[10\%\]{left:10%}.left-\[55\%\]{left:55%}.right-6{right:1.5rem}.top-1\/2{top:50%}.z-0{z-index:0}.z-10{z-index:10}.z-20{z-index:20}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.mb-12{margin-bottom:3rem}.mb-16{margin-bottom:4rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.ml-3{margin-left:0.75rem}.mr-3{margin-right:0.75rem}.mt-12{margin-top:3rem}.mb-2{margin-bottom:0.5rem}.ml-2{margin-left:0.5rem}.mr-1{margin-right:0.25rem}.mr-2{margin-right:0.5rem}.mb-1{margin-bottom:0.25rem}.mb-24{margin-bottom:6rem}.mr-4{margin-right:1rem}.mt-1{margin-top:0.25rem}.-ml-8{margin-left:-2rem}.inline-block{display:inline-block}.flex{display:flex}.inline-flex{display:inline-flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-12{height:3rem}.h-8{height:2rem}.h-screen{height:100vh}.h-48{height:12rem}.h-14{height:3.5rem}.h-64{height:16rem}.h-full{height:100%}.h-\[72px\]{height:72px}.h-16{height:4rem}.h-\[400px\]{height:400px}.h-\[480px\]{height:480px}.h-auto{height:auto}.min-h-screen{min-height:100vh}.w-10{width:2.5rem}.w-12{width:3rem}.w-8{width:2rem}.w-full{width:100%}.w-14{width:3.5rem}.w-1\/2{width:50%}.w-16{width:4rem}.w-2\/3{width:66.666667%}.w-\[360px\]{width:360px}.w-\[40\%\]{width:40%}.max-w-2xl{max-width:42rem}.max-w-5xl{max-width:64rem}.max-w-6xl{max-width:72rem}.max-w-7xl{max-width:80rem}.max-w-xs{max-width:20rem}.max-w-3xl{max-width:48rem}.max-w-\[80\%\]{max-width:80%}.max-w-md{max-width:28rem}.flex-1{flex:1 1 0%}.flex-grow{flex-grow:1}.origin-bottom-right{transform-origin:bottom right}.-translate-y-1\/2{--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.scale-150{--tw-scale-x:1.5;--tw-scale-y:1.5;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:0.5rem}.gap-4{gap:1rem}.gap-8{gap:2rem}.gap-12{gap:3rem}.gap-16{gap:4rem}.gap-3{gap:0.75rem}.gap-6{gap:1.5rem}.-space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(-1rem * var(--tw-space-x-reverse));margin-left:calc(-1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-6 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.overflow-hidden{overflow:hidden}.overflow-y-auto{overflow-y:auto}.whitespace-nowrap{white-space:nowrap}.rounded-\[100px\]{border-radius:100px}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:16px}.rounded-xl{border-radius:20px}.rounded-2xl{border-radius:24px}.rounded-3xl{border-radius:32px}.rounded-tl-none{border-top-left-radius:0px}.border{border-width:1px}.border-2{border-width:2px}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.border-none{border-style:none}.border-white{--tw-border-opacity:1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}.border-white\/10{border-color:rgb(255 255 255 / 0.1)}.border-gray-800{--tw-border-opacity:1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.bg-gray-700{--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1))}.bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.bg-gray-900{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.bg-primary{--tw-bg-opacity:1;background-color:rgb(139 92 246 / var(--tw-bg-opacity, 1))}.bg-white\/10{background-color:rgb(255 255 255 / 0.1)}.bg-blue-500\/10{background-color:rgb(59 130 246 / 0.1)}.bg-blue-500\/20{background-color:rgb(59 130 246 / 0.2)}.bg-gray-950{--tw-bg-opacity:1;background-color:rgb(3 7 18 / var(--tw-bg-opacity, 1))}.bg-pink-500\/10{background-color:rgb(236 72 153 / 0.1)}.bg-pink-500\/20{background-color:rgb(236 72 153 / 0.2)}.bg-primary\/10{background-color:rgb(139 92 246 / 0.1)}.bg-primary\/20{background-color:rgb(139 92 246 / 0.2)}.bg-purple-500\/10{background-color:rgb(168 85 247 / 0.1)}.bg-purple-500\/20{background-color:rgb(168 85 247 / 0.2)}.bg-opacity-30{--tw-bg-opacity:0.3}.bg-opacity-50{--tw-bg-opacity:0.5}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.bg-gradient-to-b{background-image:linear-gradient(to bottom, var(--tw-gradient-stops))}.from-\[\#6366f1\]{--tw-gradient-from:#6366f1 var(--tw-gradient-from-position);--tw-gradient-to:rgb(99 102 241 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-primary\/80{--tw-gradient-from:rgb(139 92 246 / 0.8) var(--tw-gradient-from-position);--tw-gradient-to:rgb(139 92 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-blue-500{--tw-gradient-from:#3b82f6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(59 130 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-black\/60{--tw-gradient-from:rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);--tw-gradient-to:rgb(0 0 0 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-primary\/20{--tw-gradient-from:rgb(139 92 246 / 0.2) var(--tw-gradient-from-position);--tw-gradient-to:rgb(139 92 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-pink-300{--tw-gradient-from:#f9a8d4 var(--tw-gradient-from-position);--tw-gradient-to:rgb(249 168 212 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.via-black\/40{--tw-gradient-to:rgb(0 0 0 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgb(0 0 0 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to)}.via-purple-300{--tw-gradient-to:rgb(216 180 254 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #d8b4fe var(--tw-gradient-via-position), var(--tw-gradient-to)}.via-purple-500{--tw-gradient-to:rgb(168 85 247 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to)}.to-\[\#a855f7\]{--tw-gradient-to:#a855f7 var(--tw-gradient-to-position)}.to-secondary\/80{--tw-gradient-to:rgb(236 72 153 / 0.8) var(--tw-gradient-to-position)}.to-purple-500{--tw-gradient-to:#a855f7 var(--tw-gradient-to-position)}.to-gray-900{--tw-gradient-to:#111827 var(--tw-gradient-to-position)}.to-secondary\/20{--tw-gradient-to:rgb(236 72 153 / 0.2) var(--tw-gradient-to-position)}.to-blue-300{--tw-gradient-to:#93c5fd var(--tw-gradient-to-position)}.to-pink-500{--tw-gradient-to:#ec4899 var(--tw-gradient-to-position)}.object-cover{object-fit:cover}.object-top{object-position:top}.p-3{padding:0.75rem}.p-4{padding:1rem}.p-8{padding:2rem}.p-6{padding:1.5rem}.px-16{padding-left:4rem;padding-right:4rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.px-8{padding-left:2rem;padding-right:2rem}.py-2\.5{padding-top:0.625rem;padding-bottom:0.625rem}.py-20{padding-top:5rem;padding-bottom:5rem}.py-24{padding-top:6rem;padding-bottom:6rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-5{padding-top:1.25rem;padding-bottom:1.25rem}.px-12{padding-left:3rem;padding-right:3rem}.px-4{padding-left:1rem;padding-right:1rem}.py-16{padding-top:4rem;padding-bottom:4rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.pb-6{padding-bottom:1.5rem}.pt-8{padding-top:2rem}.text-left{text-align:left}.text-center{text-align:center}.font-\[\'Pacifico\'\]{font-family:'Pacifico'}.text-2xl{font-size:1.5rem;line-height:2rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-5xl{font-size:3rem;line-height:1}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.italic{font-style:italic}.leading-tight{line-height:1.25}.text-gray-200{--tw-text-opacity:1;color:rgb(229 231 235 / var(--tw-text-opacity, 1))}.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.text-primary{--tw-text-opacity:1;color:rgb(139 92 246 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-yellow-400{--tw-text-opacity:1;color:rgb(250 204 21 / var(--tw-text-opacity, 1))}.text-green-500{--tw-text-opacity:1;color:rgb(34 197 94 / var(--tw-text-opacity, 1))}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-pink-500{--tw-text-opacity:1;color:rgb(236 72 153 / var(--tw-text-opacity, 1))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.opacity-0{opacity:0}.shadow-\[0_0_20px_rgba\(139\2c 92\2c 246\2c 0\.3\)\]{--tw-shadow:0 0 20px rgba(139,92,246,0.3);--tw-shadow-colored:0 0 20px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-2xl{--tw-shadow:0 25px 50px -12px rgb(0 0 0 / 0.25);--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-black\/5{--tw-shadow-color:rgb(0 0 0 / 0.05);--tw-shadow:var(--tw-shadow-colored)}.shadow-primary\/20{--tw-shadow-color:rgb(139 92 246 / 0.2);--tw-shadow:var(--tw-shadow-colored)}.blur-lg{--tw-blur:blur(16px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.brightness-75{--tw-brightness:brightness(.75);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.backdrop-blur-md{--tw-backdrop-blur:blur(12px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.duration-300{transition-duration:300ms}.duration-500{transition-duration:500ms}.after\:absolute::after{content:var(--tw-content);position:absolute}.after\:bottom-0::after{content:var(--tw-content);bottom:0px}.after\:left-0::after{content:var(--tw-content);left:0px}.after\:h-\[2px\]::after{content:var(--tw-content);height:2px}.after\:w-0::after{content:var(--tw-content);width:0px}.after\:bg-primary::after{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(139 92 246 / var(--tw-bg-opacity, 1))}.after\:transition-all::after{content:var(--tw-content);transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.after\:duration-300::after{content:var(--tw-content);transition-duration:300ms}.after\:content-\[\'\'\]::after{--tw-content:'';content:var(--tw-content)}.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:bg-gray-800:hover{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.hover\:bg-primary:hover{--tw-bg-opacity:1;background-color:rgb(139 92 246 / var(--tw-bg-opacity, 1))}.hover\:bg-primary\/90:hover{background-color:rgb(139 92 246 / 0.9)}.hover\:from-primary:hover{--tw-gradient-from:#8b5cf6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(139 92 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:to-secondary:hover{--tw-gradient-to:#ec4899 var(--tw-gradient-to-position)}.hover\:text-primary:hover{--tw-text-opacity:1;color:rgb(139 92 246 / var(--tw-text-opacity, 1))}.hover\:text-secondary:hover{--tw-text-opacity:1;color:rgb(236 72 153 / var(--tw-text-opacity, 1))}.hover\:text-white:hover{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.hover\:opacity-90:hover{opacity:0.9}.hover\:shadow-\[0_0_25px_rgba\(139\2c 92\2c 246\2c 0\.4\)\]:hover{--tw-shadow:0 0 25px rgba(139,92,246,0.4);--tw-shadow-colored:0 0 25px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.hover\:shadow-xl:hover{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.hover\:after\:w-full:hover::after{content:var(--tw-content);width:100%}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-primary\/50:focus{--tw-ring-color:rgb(139 92 246 / 0.5)}.group:hover .group-hover\:scale-105{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group:hover .group-hover\:from-black\/80{--tw-gradient-from:rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);--tw-gradient-to:rgb(0 0 0 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.group:hover .group-hover\:via-black\/60{--tw-gradient-to:rgb(0 0 0 / 0)  var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), rgb(0 0 0 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to)}.group:hover .group-hover\:to-gray-900{--tw-gradient-to:#111827 var(--tw-gradient-to-position)}.group:hover .group-hover\:opacity-100{opacity:1}@media (min-width: 640px){.sm\:flex-row{flex-direction:row}}@media (min-width: 768px){.md\:col-span-2{grid-column:span 2 / span 2}.md\:mb-0{margin-bottom:0px}.md\:inline-block{display:inline-block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:flex-1{flex:1 1 0%}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.md\:grid-cols-5{grid-template-columns:repeat(5, minmax(0, 1fr))}.md\:flex-row{flex-direction:row}.md\:text-2xl{font-size:1.5rem;line-height:2rem}.md\:text-7xl{font-size:4.5rem;line-height:1}}</style><link type="image/png" rel="icon" href="https://public.readdy.ai/gen_page/readdy-logo.png"><link rel="stylesheet" href="./assets/css2(1)"></head>
<body class="bg-black text-white min-h-screen relative">
<!-- Hero Section with Video Background -->
<div class="relative min-h-screen overflow-hidden">
<!-- 視頻背景 -->
<video class="absolute inset-0 w-full h-full object-cover z-0" autoplay muted loop playsinline>
<source src="./assets/1.mp4" type="video/mp4">
<!-- 如果視頻無法載入，顯示備用背景 -->
<div class="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-black"></div>
</video>
<!-- 視頻遮罩層 -->
<div class="absolute inset-0 bg-black bg-opacity-40 z-10"></div>
<!-- Navigation -->
<nav class="fixed w-full top-0 left-0 z-50 px-6 py-4">
<div class="max-w-6xl mx-auto">
<div class="bg-white/10 backdrop-blur-md rounded-full border border-white/10 shadow-lg shadow-black/5 px-6 py-3 flex items-center justify-between">
<a href="#sec1" class="flex items-center group transition-all duration-300 hover:scale-105">
<!-- Logo 圖標 -->
<div class="relative mr-3">
<div class="w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:shadow-purple-500/25 transition-all duration-300">
<i class="ri-infinity-line text-white text-xl group-hover:rotate-180 transition-transform duration-500"></i>
</div>
<!-- 動態光環 -->
<div class="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-400 to-pink-400 rounded-xl opacity-0 group-hover:opacity-20 blur-sm transition-all duration-300"></div>
</div>
<!-- Logo 文字 -->
<div class="flex flex-col justify-center">
<span class="text-3xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent group-hover:from-blue-200 group-hover:via-purple-200 group-hover:to-pink-200 transition-all duration-300 tracking-tight transform group-hover:scale-90 group-hover:-translate-y-1 leading-none">InfiniFlow</span>
<span class="text-xs text-gray-300 font-medium tracking-wider opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">AI WORKFLOW</span>
</div>
</a>
<div class="hidden md:flex-1 md:flex items-center justify-center space-x-8">
<div class="flex items-center space-x-8" id="nav-links">
<a href="#sec1" class="nav-link text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">首頁</a>
<a href="#sec2" class="nav-link text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">智慧工作流</a>
<a href="#use-cases-sec" class="nav-link text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">成功案例</a>
<a href="#sec4" class="nav-link text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">定價方案</a>
<a href="#sec5" class="nav-link text-white hover:text-primary transition-all duration-300 relative after:content-[&#39;&#39;] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-primary hover:after:w-full after:transition-all after:duration-300">常見問題</a>
</div>
</div>
<a href="#sec1" class="hidden md:inline-block px-8 py-2.5 bg-gradient-to-r from-primary/80 to-secondary/80 hover:from-primary hover:to-secondary rounded-full text-white font-medium transition-all duration-300 shadow-[0_0_20px_rgba(139,92,246,0.3)] hover:shadow-[0_0_25px_rgba(139,92,246,0.4)] hover:scale-105">立即開始</a>

<!-- 手機版選單按鈕 -->
<button id="mobile-menu-btn" class="md:hidden w-10 h-10 flex items-center justify-center text-white hover:text-primary transition-colors duration-300">
<i class="ri-menu-line ri-lg"></i>
</button>
</div>

<!-- 手機版選單 -->
<div id="mobile-menu" class="md:hidden fixed top-0 left-0 w-full h-full bg-black/95 backdrop-blur-md transform -translate-x-full transition-transform duration-300 ease-in-out z-50">
<div class="flex flex-col h-full">
<!-- 手機版選單標題 -->
<div class="flex items-center justify-between p-6 border-b border-gray-700">
<!-- 手機版 Logo -->
<div class="flex items-center">
<div class="w-8 h-8 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
<i class="ri-infinity-line text-white text-lg"></i>
</div>
<div class="flex flex-col">
<span class="text-xl font-bold bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent tracking-tight">InfiniFlow</span>
<span class="text-xs text-gray-400 font-medium tracking-wider">AI WORKFLOW</span>
</div>
</div>
<button id="mobile-menu-close" class="w-10 h-10 flex items-center justify-center text-white hover:text-primary transition-colors duration-300">
<i class="ri-close-line ri-lg"></i>
</button>
</div>

<!-- 手機版選單項目 -->
<div class="flex-1 flex flex-col justify-center space-y-8 px-6">
<a href="#sec1" class="mobile-nav-link text-2xl text-white hover:text-primary transition-all duration-300 text-center py-4 border-b border-gray-800 hover:border-primary">首頁</a>
<a href="#sec2" class="mobile-nav-link text-2xl text-white hover:text-primary transition-all duration-300 text-center py-4 border-b border-gray-800 hover:border-primary">智慧工作流</a>
<a href="#use-cases-sec" class="mobile-nav-link text-2xl text-white hover:text-primary transition-all duration-300 text-center py-4 border-b border-gray-800 hover:border-primary">成功案例</a>
<a href="#sec4" class="mobile-nav-link text-2xl text-white hover:text-primary transition-all duration-300 text-center py-4 border-b border-gray-800 hover:border-primary">定價方案</a>
<a href="#sec5" class="mobile-nav-link text-2xl text-white hover:text-primary transition-all duration-300 text-center py-4 border-b border-gray-800 hover:border-primary">常見問題</a>
</div>

<!-- 手機版 CTA 按鈕 -->
<div class="p-6">
<a href="#sec1" class="block w-full text-center px-8 py-4 bg-gradient-to-r from-primary/80 to-secondary/80 hover:from-primary hover:to-secondary rounded-full text-white font-medium transition-all duration-300 shadow-lg">立即開始</a>
</div>
</div>
</div>
</div>
</nav>
<!-- Hero Content -->
<div class="relative z-20 flex flex-col items-center justify-center px-6 py-24 text-center h-screen" id="sec1">
<span class="text-primary font-medium mb-4">AI 驅動行銷平台</span>
<h1 class="text-5xl md:text-7xl font-bold mb-8 max-w-5xl leading-tight">用 <span class="gradient-text">智慧自動化</span> 重新定義您的行銷</h1>
<p class="text-xl md:text-2xl text-gray-200 mb-12 max-w-3xl">透過 AI 驅動的洞察分析、個人化行銷活動與自動化工作流程，讓您的業績成長速度提升 10 倍。</p>
<div class="flex flex-col sm:flex-row items-center gap-4">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="btn-hover bg-gradient-to-r from-[#6366f1] to-[#a855f7] text-white text-xl font-medium px-16 py-5 rounded-[100px] shadow-lg hover:shadow-xl transition-all duration-300 whitespace-nowrap">免費試用</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="flex items-center gap-2 text-xl font-medium hover:text-primary transition-colors">
<i class="ri-play-circle-line ri-xl"></i>
觀看示範
</a>
</div>
<div class="flex items-center gap-8 mt-12">
<div class="flex -space-x-4">
<img src="./assets/search-image" alt="用戶頭像" class="w-12 h-12 rounded-full border-2 border-white">
<img src="./assets/search-image(1)" alt="用戶頭像" class="w-12 h-12 rounded-full border-2 border-white">
<img src="./assets/search-image(2)" alt="用戶頭像" class="w-12 h-12 rounded-full border-2 border-white">
</div>
<div class="flex items-center gap-2">
<div class="flex items-center">
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
<i class="ri-star-fill ri-sm text-yellow-400"></i>
</div>
<p class="text-gray-300">超過 10,000+ 行銷團隊的信賴選擇</p>
</div>
</div>
</div>
</div>
<!-- Smart Workflow Showcase Section -->
<section class="py-20 px-6 bg-gradient-to-b from-black via-gray-900/50 to-black" id="sec2">
<div class="max-w-7xl mx-auto">
<div class="text-center mb-20">
<div class="inline-flex items-center px-4 py-2 rounded-full bg-purple-500/10 border border-purple-500/20 mb-6">
<i class="ri-magic-line text-purple-400 mr-2"></i>
<span class="text-purple-400 text-sm font-medium">見證工作流程的革新</span>
</div>
<h3 class="text-5xl font-bold text-white mb-6">
Smart <span class="gradient-text">Workflow</span> Showcase
</h3>
<p class="text-gray-300 max-w-4xl mx-auto text-xl leading-relaxed">
InfiniFlow™ 是一個<span class="text-blue-400 font-semibold">革命性的工作流程引擎</span>。整合了AI模型、高效的內容生產平台與您日常使用的商業工具，將複雜的營運流程<span class="text-green-400 font-semibold">完全自動化</span>。
</p>
</div>

<!-- 2x2 Grid Layout -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-10 max-w-7xl mx-auto">

<!-- 左上卡片 - 跨平台觸發 -->
<div class="group relative bg-gradient-to-br from-blue-900/20 via-gray-900/80 to-gray-800/60 backdrop-blur-sm rounded-3xl p-8 border border-blue-500/20 hover:border-blue-400/40 transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-2">
<!-- 背景光效 -->
<div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

<div class="relative z-10">
<!-- 標題區域 -->
<div class="flex items-center mb-6">
<div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
<i class="ri-flashlight-line text-white text-xl"></i>
</div>
<div>
<h4 class="text-2xl font-bold text-white mb-1">跨平台觸發</h4>
<span class="text-blue-400 text-sm font-medium">Trigger</span>
</div>
</div>

<p class="text-gray-300 text-lg leading-relaxed mb-8">
<span class="text-blue-400 font-semibold">一句話、一個動作</span>，即可從您熟悉的工具啟動。無縫串連<span class="text-green-400">LINE、Google Sheets</span>等數十種應用，讓工作流程觸手可及。
</p>

<!-- 增強的即時通訊UI -->
<div class="bg-gradient-to-br from-gray-800/80 to-gray-900/60 rounded-2xl p-6 border border-gray-600/30 backdrop-blur-sm">
<div class="flex items-center justify-between mb-6">
<div class="flex items-center">
<div class="w-3 h-3 rounded-full bg-red-500 mr-2 animate-pulse"></div>
<div class="w-3 h-3 rounded-full bg-yellow-500 mr-2 animate-pulse" style="animation-delay: 0.2s"></div>
<div class="w-3 h-3 rounded-full bg-green-500 animate-pulse" style="animation-delay: 0.4s"></div>
</div>
<div class="flex items-center">
<i class="ri-line-line text-green-500 mr-2"></i>
<span class="text-sm text-gray-300 font-medium">LINE Business</span>
</div>
</div>

<div class="space-y-4">
<div class="flex justify-end">
<div class="bg-gradient-to-r from-blue-600 to-blue-500 rounded-2xl rounded-br-md px-5 py-3 max-w-sm shadow-lg">
<p class="text-white font-medium">🚀 生成Q3銷售報告的摘要網頁</p>
<div class="flex items-center justify-between mt-2">
<span class="text-xs text-blue-100">剛剛</span>
<i class="ri-check-double-line text-blue-200"></i>
</div>
</div>
</div>

<div class="flex items-center">
<div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
<i class="ri-robot-line text-white text-sm"></i>
</div>
<div class="flex-1">
<div class="flex items-center">
<div class="flex space-x-1 mr-3">
<div class="w-2 h-2 bg-green-400 rounded-full animate-bounce"></div>
<div class="w-2 h-2 bg-green-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
<div class="w-2 h-2 bg-green-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
</div>
<span class="text-sm text-gray-300">AI 正在分析數據並生成內容...</span>
</div>
</div>
</div>

<!-- 進度條 -->
<div class="mt-4 bg-gray-700/50 rounded-full h-2 overflow-hidden">
<div class="bg-gradient-to-r from-blue-500 to-cyan-400 h-full rounded-full animate-pulse" style="width: 75%"></div>
</div>
</div>
</div>
</div>
</div>

<!-- 右上卡片 - AI思考與創造 -->
<div class="group relative bg-gradient-to-br from-purple-900/20 via-gray-900/80 to-gray-800/60 backdrop-blur-sm rounded-3xl p-8 border border-purple-500/20 hover:border-purple-400/40 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2">
<!-- 背景光效 -->
<div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

<div class="relative z-10">
<!-- 標題區域 -->
<div class="flex items-center mb-6">
<div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-400 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
<i class="ri-brain-line text-white text-xl"></i>
</div>
<div>
<h4 class="text-2xl font-bold text-white mb-1">AI思考與創造</h4>
<span class="text-purple-400 text-sm font-medium">Create</span>
</div>
</div>

<p class="text-gray-300 text-lg leading-relaxed mb-8">
<span class="text-purple-400 font-semibold">AI大腦</span>自動生成世界級文案、尋找匹配視覺素材，並即時完成數據整理。讓<span class="text-pink-400">創意與效率</span>完美結合。
</p>

<!-- 增強的AI處理核心視覺 -->
<div class="relative bg-gradient-to-br from-gray-800/80 to-gray-900/60 rounded-2xl p-8 border border-gray-600/30 backdrop-blur-sm overflow-hidden">
<!-- 動態背景 -->
<div class="absolute inset-0">
<div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-blue-500/5 animate-pulse"></div>
<div class="absolute top-1/4 left-1/4 w-32 h-32 bg-purple-500/10 rounded-full blur-xl animate-ping"></div>
<div class="absolute bottom-1/4 right-1/4 w-24 h-24 bg-pink-500/10 rounded-full blur-xl animate-ping" style="animation-delay: 1s"></div>
</div>

<div class="relative">
<!-- AI核心 -->
<div class="flex items-center justify-center mb-8">
<div class="relative">
<div class="w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 flex items-center justify-center animate-spin-slow">
<div class="w-16 h-16 rounded-full bg-gray-900 flex items-center justify-center">
<i class="ri-brain-line text-3xl text-white animate-pulse"></i>
</div>
</div>
<!-- 環繞粒子 -->
<div class="absolute -top-2 -left-2 w-6 h-6 bg-purple-400 rounded-full animate-bounce opacity-60"></div>
<div class="absolute -top-2 -right-2 w-4 h-4 bg-pink-400 rounded-full animate-bounce opacity-60" style="animation-delay: 0.5s"></div>
<div class="absolute -bottom-2 -left-2 w-5 h-5 bg-blue-400 rounded-full animate-bounce opacity-60" style="animation-delay: 1s"></div>
<div class="absolute -bottom-2 -right-2 w-3 h-3 bg-cyan-400 rounded-full animate-bounce opacity-60" style="animation-delay: 1.5s"></div>
</div>
</div>

<!-- 輸出展示 -->
<div class="grid grid-cols-3 gap-6">
<div class="text-center group/item">
<div class="w-12 h-12 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover/item:scale-110 transition-transform duration-300 border border-green-500/30">
<span class="text-green-400 font-bold text-lg">Abc</span>
</div>
<span class="text-sm text-gray-300 font-medium">智能文案</span>
<div class="mt-2 h-1 bg-gray-700 rounded-full overflow-hidden">
<div class="h-full bg-gradient-to-r from-green-400 to-emerald-400 rounded-full animate-pulse" style="width: 85%"></div>
</div>
</div>

<div class="text-center group/item">
<div class="w-12 h-12 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover/item:scale-110 transition-transform duration-300 border border-blue-500/30">
<i class="ri-image-line text-blue-400 text-xl"></i>
</div>
<span class="text-sm text-gray-300 font-medium">視覺素材</span>
<div class="mt-2 h-1 bg-gray-700 rounded-full overflow-hidden">
<div class="h-full bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-pulse" style="width: 92%; animation-delay: 0.5s"></div>
</div>
</div>

<div class="text-center group/item">
<div class="w-12 h-12 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover/item:scale-110 transition-transform duration-300 border border-purple-500/30">
<i class="ri-bar-chart-line text-purple-400 text-xl"></i>
</div>
<span class="text-sm text-gray-300 font-medium">數據分析</span>
<div class="mt-2 h-1 bg-gray-700 rounded-full overflow-hidden">
<div class="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse" style="width: 78%; animation-delay: 1s"></div>
</div>
</div>
</div>

<!-- 處理狀態 -->
<div class="mt-6 flex items-center justify-center">
<div class="flex items-center bg-gray-800/50 rounded-full px-4 py-2 border border-gray-600/30">
<div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
<span class="text-sm text-gray-300">正在創造中...</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- 左下卡片 - 秒速建構 -->
<div class="group relative bg-gradient-to-br from-green-900/20 via-gray-900/80 to-gray-800/60 backdrop-blur-sm rounded-3xl p-8 border border-green-500/20 hover:border-green-400/40 transition-all duration-500 hover:shadow-2xl hover:shadow-green-500/10 hover:-translate-y-2">
<!-- 背景光效 -->
<div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

<div class="relative z-10">
<!-- 標題區域 -->
<div class="flex items-center mb-6">
<div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-400 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
<i class="ri-rocket-line text-white text-xl"></i>
</div>
<div>
<h4 class="text-2xl font-bold text-white mb-1">秒速建構</h4>
<span class="text-green-400 text-sm font-medium">Build</span>
</div>
</div>

<p class="text-gray-300 text-lg leading-relaxed mb-8">
內容自動填入<span class="text-green-400 font-semibold">精美範本</span>，在數秒內為您建構出可立即發布的<span class="text-emerald-400">專業級網頁或報告</span>。效率與品質並重。
</p>

<!-- 增強的網頁編輯器模擬 -->
<div class="relative bg-gradient-to-br from-gray-800/80 to-gray-900/60 rounded-2xl p-6 border border-gray-600/30 backdrop-blur-sm overflow-hidden">
<!-- 瀏覽器標題欄 -->
<div class="flex items-center justify-between mb-6">
<div class="flex items-center">
<div class="w-3 h-3 rounded-full bg-red-500 mr-2 animate-pulse"></div>
<div class="w-3 h-3 rounded-full bg-yellow-500 mr-2 animate-pulse" style="animation-delay: 0.2s"></div>
<div class="w-3 h-3 rounded-full bg-green-500 animate-pulse" style="animation-delay: 0.4s"></div>
</div>
<div class="flex items-center bg-gray-700/50 rounded-lg px-3 py-1">
<i class="ri-global-line text-gray-400 mr-2 text-sm"></i>
<span class="text-xs text-gray-400">infiniflow-demo.com</span>
</div>
</div>

<!-- 建構進度 -->
<div class="mb-6">
<div class="flex items-center justify-between mb-2">
<span class="text-sm text-gray-300 font-medium">建構進度</span>
<div class="flex items-center">
<span class="text-green-400 text-sm font-bold mr-2">100% 完成</span>
<div class="w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-400 rounded-full flex items-center justify-center">
<i class="ri-check-line text-xs text-white"></i>
</div>
</div>
</div>
<div class="w-full bg-gray-700/50 rounded-full h-2 overflow-hidden">
<div class="bg-gradient-to-r from-green-500 to-emerald-400 h-full rounded-full transition-all duration-2000 ease-out" style="width: 100%"></div>
</div>
</div>

<!-- 模擬網頁內容 -->
<div class="space-y-4">
<!-- 標題區塊 -->
<div class="bg-gradient-to-r from-gray-700/30 to-gray-600/30 rounded-xl p-4 border border-gray-600/20">
<div class="flex items-center mb-3">
<div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-lg mr-3 animate-pulse"></div>
<div class="h-3 bg-gradient-to-r from-gray-500 to-gray-400 rounded-full flex-1 animate-pulse"></div>
</div>
<div class="space-y-2">
<div class="h-2 bg-gradient-to-r from-gray-500 to-gray-400 rounded-full w-full animate-pulse"></div>
<div class="h-2 bg-gradient-to-r from-gray-500 to-gray-400 rounded-full w-4/5 animate-pulse" style="animation-delay: 0.2s"></div>
<div class="h-2 bg-gradient-to-r from-gray-500 to-gray-400 rounded-full w-3/5 animate-pulse" style="animation-delay: 0.4s"></div>
</div>
</div>

<!-- 數據區塊 -->
<div class="grid grid-cols-2 gap-3">
<div class="bg-gradient-to-r from-gray-700/30 to-gray-600/30 rounded-lg p-3 border border-gray-600/20">
<div class="flex items-center justify-between mb-2">
<div class="h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full w-1/2 animate-pulse"></div>
<div class="h-2 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full w-1/4 animate-pulse"></div>
</div>
<div class="h-1 bg-gray-600/50 rounded-full w-full"></div>
</div>
<div class="bg-gradient-to-r from-gray-700/30 to-gray-600/30 rounded-lg p-3 border border-gray-600/20">
<div class="flex items-center justify-between mb-2">
<div class="h-2 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full w-2/3 animate-pulse" style="animation-delay: 0.3s"></div>
<div class="h-2 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full w-1/3 animate-pulse" style="animation-delay: 0.6s"></div>
</div>
<div class="h-1 bg-gray-600/50 rounded-full w-full"></div>
</div>
</div>
</div>

<!-- 建構完成提示 -->
<div class="mt-4 flex items-center justify-center">
<div class="flex items-center bg-green-500/10 border border-green-500/30 rounded-full px-4 py-2">
<i class="ri-check-double-line text-green-400 mr-2"></i>
<span class="text-sm text-green-400 font-medium">網頁建構完成，準備發布！</span>
</div>
</div>

<!-- 動態光效 -->
<div class="absolute inset-0 pointer-events-none">
<div class="absolute top-1/3 left-1/3 w-24 h-24 bg-green-500/10 rounded-full blur-xl animate-ping"></div>
<div class="absolute bottom-1/3 right-1/3 w-32 h-32 bg-emerald-500/10 rounded-full blur-xl animate-ping" style="animation-delay: 1s"></div>
</div>
</div>
</div>
</div>
<!-- 右下卡片 - 全通路分發 -->
<div class="group relative bg-gradient-to-br from-orange-900/20 via-gray-900/80 to-gray-800/60 backdrop-blur-sm rounded-3xl p-8 border border-orange-500/20 hover:border-orange-400/40 transition-all duration-500 hover:shadow-2xl hover:shadow-orange-500/10 hover:-translate-y-2">
<!-- 背景光效 -->
<div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

<div class="relative z-10">
<!-- 標題區域 -->
<div class="flex items-center mb-6">
<div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-400 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
<i class="ri-share-line text-white text-xl"></i>
</div>
<div>
<h4 class="text-2xl font-bold text-white mb-1">全通路分發</h4>
<span class="text-orange-400 text-sm font-medium">Distribute</span>
</div>
</div>

<p class="text-gray-300 text-lg leading-relaxed mb-8">
<span class="text-orange-400 font-semibold">一鍵發布</span>，自動將內容分發至社群、郵件與團隊頻道，<span class="text-red-400">最大化您的影響力</span>。觸及每一個潛在客戶。
</p>

<!-- 增強的分析儀表板模擬 -->
<div class="relative bg-gradient-to-br from-gray-800/80 to-gray-900/60 rounded-2xl p-6 border border-gray-600/30 backdrop-blur-sm overflow-hidden">
<!-- 儀表板標題 -->
<div class="flex items-center justify-between mb-6">
<div class="flex items-center">
<i class="ri-dashboard-line text-orange-400 mr-2"></i>
<span class="text-sm text-gray-300 font-medium">分發儀表板</span>
</div>
<div class="flex items-center bg-green-500/10 border border-green-500/30 rounded-full px-3 py-1">
<div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
<span class="text-xs text-green-400 font-medium">即時同步中</span>
</div>
</div>

<!-- 觸及率圖表 -->
<div class="mb-6">
<div class="flex items-center justify-between mb-3">
<span class="text-sm text-gray-300 font-medium">全平台觸及率</span>
<div class="flex items-center">
<i class="ri-arrow-up-line text-green-400 mr-1"></i>
<span class="text-green-400 text-lg font-bold">+127%</span>
</div>
</div>

<!-- 增強的線條圖 -->
<div class="relative h-20 bg-gray-700/30 rounded-xl overflow-hidden p-2">
<svg class="w-full h-full" viewBox="0 0 300 80">
<defs>
<linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="0%">
<stop offset="0%" style="stop-color:#F97316;stop-opacity:1" />
<stop offset="50%" style="stop-color:#EF4444;stop-opacity:1" />
<stop offset="100%" style="stop-color:#10B981;stop-opacity:1" />
</linearGradient>
<linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
<stop offset="0%" style="stop-color:#F97316;stop-opacity:0.3" />
<stop offset="100%" style="stop-color:#F97316;stop-opacity:0" />
</linearGradient>
</defs>
<!-- 面積填充 -->
<polygon
fill="url(#areaGradient)"
points="20,70 60,55 100,40 140,30 180,20 220,15 260,10 280,8 280,70 20,70"
class="animate-pulse"
/>
<!-- 線條 -->
<polyline
fill="none"
stroke="url(#chartGradient)"
stroke-width="3"
points="20,70 60,55 100,40 140,30 180,20 220,15 260,10 280,8"
class="animate-pulse"
/>
<!-- 數據點 -->
<circle cx="280" cy="8" r="4" fill="#10B981" class="animate-ping"/>
</svg>
</div>
</div>

<!-- 平台分發狀態 -->
<div class="space-y-4">
<div class="text-sm text-gray-400 font-medium mb-3">分發平台狀態</div>
<div class="grid grid-cols-2 gap-4">
<!-- 第一排平台 -->
<div class="flex items-center justify-between bg-gray-700/30 rounded-lg p-3">
<div class="flex items-center">
<div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3">
<i class="ri-facebook-fill text-blue-500"></i>
</div>
<span class="text-sm text-gray-300">Facebook</span>
</div>
<div class="flex items-center">
<span class="text-xs text-green-400 mr-2">已發布</span>
<div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
<i class="ri-check-line text-xs text-white"></i>
</div>
</div>
</div>

<div class="flex items-center justify-between bg-gray-700/30 rounded-lg p-3">
<div class="flex items-center">
<div class="w-8 h-8 bg-pink-500/20 rounded-lg flex items-center justify-center mr-3">
<i class="ri-instagram-line text-pink-500"></i>
</div>
<span class="text-sm text-gray-300">Instagram</span>
</div>
<div class="flex items-center">
<span class="text-xs text-green-400 mr-2">已發布</span>
<div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
<i class="ri-check-line text-xs text-white"></i>
</div>
</div>
</div>

<!-- 第二排平台 -->
<div class="flex items-center justify-between bg-gray-700/30 rounded-lg p-3">
<div class="flex items-center">
<div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center mr-3">
<i class="ri-mail-line text-red-500"></i>
</div>
<span class="text-sm text-gray-300">Email</span>
</div>
<div class="flex items-center">
<span class="text-xs text-yellow-400 mr-2">發送中</span>
<div class="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center animate-spin">
<i class="ri-loader-line text-xs text-white"></i>
</div>
</div>
</div>

<div class="flex items-center justify-between bg-gray-700/30 rounded-lg p-3">
<div class="flex items-center">
<div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mr-3">
<i class="ri-slack-line text-purple-500"></i>
</div>
<span class="text-sm text-gray-300">Slack</span>
</div>
<div class="flex items-center">
<span class="text-xs text-green-400 mr-2">已發布</span>
<div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
<i class="ri-check-line text-xs text-white"></i>
</div>
</div>
</div>
</div>
</div>

<!-- 總結統計 -->
<div class="mt-4 flex items-center justify-center">
<div class="flex items-center bg-orange-500/10 border border-orange-500/30 rounded-full px-4 py-2">
<i class="ri-global-line text-orange-400 mr-2"></i>
<span class="text-sm text-orange-400 font-medium">已觸及 2.4M+ 用戶</span>
</div>
</div>

<!-- 動態光效 -->
<div class="absolute inset-0 pointer-events-none">
<div class="absolute top-1/4 right-1/4 w-20 h-20 bg-orange-500/10 rounded-full blur-xl animate-ping"></div>
<div class="absolute bottom-1/4 left-1/4 w-28 h-28 bg-red-500/10 rounded-full blur-xl animate-ping" style="animation-delay: 1.5s"></div>
</div>
</div>
</div>
</div>
</div>
</div>
</section>

<!-- 自定義動畫樣式 -->
<style>
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 4s linear infinite;
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 卡片懸停效果增強 */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* 自定義動畫延遲 */
.animate-bounce-delay-1 {
  animation: bounce 1s infinite;
  animation-delay: 0.5s;
}

.animate-bounce-delay-2 {
  animation: bounce 1s infinite;
  animation-delay: 1s;
}

.animate-bounce-delay-3 {
  animation: bounce 1s infinite;
  animation-delay: 1.5s;
}
</style>
<script type="text/javascript" crossorigin="anonymous" src="./assets/surveys.js"></script><script type="text/javascript" crossorigin="anonymous" src="./assets/dead-clicks-autocapture.js"></script><script type="text/javascript" crossorigin="anonymous" src="./assets/config.js"></script><script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>
<!-- Use Cases Section -->
<section class="relative py-24 px-6 bg-gradient-to-b from-gray-950 via-gray-900/80 to-gray-950 overflow-hidden" id="use-cases-sec">
<!-- 背景裝飾 -->
<div class="absolute inset-0 opacity-30">
<div class="absolute top-20 left-10 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl"></div>
<div class="absolute bottom-20 right-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
<div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-purple-500/5 to-blue-500/5 rounded-full blur-3xl"></div>
</div>

<div class="relative max-w-7xl mx-auto">
<!-- 標題區域 -->
<div class="text-center mb-20">
<div class="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 mb-8">
<i class="ri-rocket-line text-purple-400 mr-3"></i>
<span class="text-purple-400 text-sm font-semibold tracking-wide">SUCCESS STORIES</span>
</div>
<h2 class="text-5xl md:text-6xl font-bold mb-8">
<span class="gradient-text">真實案例</span>見證
<br/>
<span class="text-white">跨產業成功故事</span>
</h2>
<p class="text-gray-300 max-w-4xl mx-auto text-xl leading-relaxed">
探索各行各業如何運用 <span class="text-blue-400 font-semibold">InfiniFlow</span> 實現業績突破，
從<span class="text-purple-400 font-semibold">傳統產業數位轉型</span>到<span class="text-green-400 font-semibold">新創企業快速成長</span>，
每一個成功故事都值得您深入了解。
</p>
</div>

<!-- 頁籤切換按鈕 -->
<div class="flex flex-wrap justify-center gap-3 mb-10">
<!-- 電商類別 -->
<button class="tab-btn active group relative overflow-hidden px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500 rounded-2xl text-white font-semibold transition-all duration-500 shadow-lg hover:shadow-purple-500/25 hover:scale-105 transform" data-tab="ecommerce1">
<div class="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-white/20 rounded-lg flex items-center justify-center mr-2">
<i class="ri-shopping-cart-line text-sm"></i>
</div>
<span class="text-sm">電商節慶活動</span>
</div>
</button>

<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="ecommerce2">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-store-line text-sm"></i>
</div>
<span class="text-sm">電商商品頁面</span>
</div>
</button>

<!-- 房地產 -->
<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="realestate">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-building-line text-sm"></i>
</div>
<span class="text-sm">房地產與仲介業</span>
</div>
</button>

<!-- 教育 -->
<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="education">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-book-line text-sm"></i>
</div>
<span class="text-sm">教育顧問與知識付費</span>
</div>
</button>

<!-- SaaS -->
<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="saas">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-cloud-line text-sm"></i>
</div>
<span class="text-sm">SaaS與軟體服務業</span>
</div>
</button>

<!-- 醫美類別 -->
<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="beauty1">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-heart-pulse-line text-sm"></i>
</div>
<span class="text-sm">醫美見證案例</span>
</div>
</button>

<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="beauty2">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-user-heart-line text-sm"></i>
</div>
<span class="text-sm">醫美智慧推薦</span>
</div>
</button>

<!-- 創作者類別 -->
<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="creator1">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-video-line text-sm"></i>
</div>
<span class="text-sm">創作者內容中樞</span>
</div>
</button>

<button class="tab-btn group relative overflow-hidden px-6 py-3 bg-gray-800/50 hover:bg-gradient-to-r hover:from-purple-600/80 hover:to-blue-600/80 rounded-2xl text-gray-300 hover:text-white font-semibold transition-all duration-500 border border-gray-600/30 hover:border-purple-500/50 hover:scale-105 transform" data-tab="creator2">
<div class="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
<div class="relative flex items-center">
<div class="w-7 h-7 bg-gray-700 group-hover:bg-white/20 rounded-lg flex items-center justify-center mr-2 transition-colors duration-300">
<i class="ri-money-dollar-circle-line text-sm"></i>
</div>
<span class="text-sm">創作者付費內容</span>
</div>
</button>
</div>

<!-- 頁籤內容區域 -->
<div class="relative" id="sec3">
<!-- 內容將由JavaScript動態生成 -->
</div>
</div>
</section>
<script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>






<!-- Features Section -->
<section class="py-20 px-6 bg-black" id="sec4">
<div class="max-w-7xl mx-auto">
<div class="text-center mb-16">
<h2 class="text-4xl font-bold mb-6">強大的功能<span class="gradient-text">旨在實現無與倫比的卓越</span></h2>
<p class="text-gray-300 max-w-2xl mx-auto text-lg">以全方位各工具的深度整合,體驗無縫工作流的最極緻效益。</p>
</div>

<!-- Features Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
<!-- 活動分析 -->
<div class="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-800/50 card-hover">
<div class="mb-8">
<h3 class="text-2xl font-bold mb-4 text-white">活動分析</h3>
<p class="text-gray-400 mb-6">即時洞察和數據驅動的行銷活動分析。</p>
</div>

<!-- 模擬圖表區域 -->
<div class="space-y-4">
<div class="flex items-center justify-between">
<span class="text-sm text-gray-400">轉換率</span>
<span class="text-sm font-medium text-green-400">+24.8%</span>
</div>
<div class="w-full bg-gray-800 rounded-full h-2">
<div class="bg-gradient-to-r from-blue-500 to-cyan-400 h-2 rounded-full" style="width: 75%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-sm text-gray-400">點擊率</span>
<span class="text-sm font-medium text-green-400">+18.3%</span>
</div>
<div class="w-full bg-gray-800 rounded-full h-2">
<div class="bg-gradient-to-r from-blue-500 to-cyan-400 h-2 rounded-full" style="width: 65%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-sm text-gray-400">投資報酬率</span>
<span class="text-sm font-medium text-green-400">+32.5%</span>
</div>
<div class="w-full bg-gray-800 rounded-full h-2">
<div class="bg-gradient-to-r from-blue-500 to-cyan-400 h-2 rounded-full" style="width: 85%"></div>
</div>
</div>
</div>

<!-- 智慧自動化 -->
<div class="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-800/50 card-hover">
<div class="mb-8">
<h3 class="text-2xl font-bold mb-4 text-white">智慧自動化</h3>
<p class="text-gray-400 mb-6">人工智慧工具可簡化您的行銷工作流程並提高效率。</p>
</div>

<!-- 自動化流程展示 -->
<div class="space-y-4">
<div class="bg-gray-800/50 rounded-lg p-4 border-l-4 border-blue-500">
<div class="flex items-center justify-between">
<div class="flex items-center">
<i class="ri-robot-line text-blue-400 mr-3"></i>
<span class="text-sm font-medium">自動回覆</span>
</div>
<span class="text-xs text-blue-400">啟用的</span>
</div>
<p class="text-xs text-gray-500 mt-2">全天候自動客戶服務</p>
</div>

<div class="bg-gray-800/50 rounded-lg p-4 border-l-4 border-purple-500">
<div class="flex items-center justify-between">
<div class="flex items-center">
<i class="ri-brain-line text-purple-400 mr-3"></i>
<span class="text-sm font-medium">智慧出價</span>
</div>
<span class="text-xs text-purple-400">啟用的</span>
</div>
<p class="text-xs text-gray-500 mt-2">自動出價最佳化</p>
</div>

<div class="bg-gray-800/50 rounded-lg p-4 border-l-4 border-green-500">
<div class="flex items-center justify-between">
<div class="flex items-center">
<i class="ri-line-chart-line text-green-400 mr-3"></i>
<span class="text-sm font-medium">A/B 測試</span>
</div>
<span class="text-xs text-green-400">啟用的</span>
</div>
<p class="text-xs text-gray-500 mt-2">自動優化</p>
</div>
</div>
</div>

<!-- 輕鬆API集成 -->
<div class="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-800/50 card-hover">
<div class="mb-8">
<h3 class="text-2xl font-bold mb-4 text-white">輕鬆API集成</h3>
<p class="text-gray-400 mb-6">透過強大的API集成，無縫連接您喜愛的電子商務平台，實現完整的數位行銷生態系統。</p>
</div>

<!-- API 集成圖標 -->
<div class="grid grid-cols-3 gap-4">
<div class="bg-blue-500/10 rounded-lg p-4 flex items-center justify-center">
<i class="ri-facebook-circle-fill text-blue-500 text-2xl"></i>
</div>
<div class="bg-red-500/10 rounded-lg p-4 flex items-center justify-center">
<i class="ri-google-fill text-red-500 text-2xl"></i>
</div>
<div class="bg-green-500/10 rounded-lg p-4 flex items-center justify-center">
<i class="ri-shield-check-line text-green-500 text-2xl"></i>
</div>
<div class="bg-purple-500/10 rounded-lg p-4 flex items-center justify-center">
<i class="ri-database-2-line text-purple-500 text-2xl"></i>
</div>
<div class="bg-yellow-500/10 rounded-lg p-4 flex items-center justify-center">
<i class="ri-amazon-line text-yellow-500 text-2xl"></i>
</div>
<div class="bg-pink-500/10 rounded-lg p-4 flex items-center justify-center">
<i class="ri-lock-line text-pink-500 text-2xl"></i>
</div>
</div>
</div>
</div>

<!-- Before/After Comparison Section -->
<div class="mt-20">
<div class="text-center mb-16">
<h2 class="text-4xl font-bold mb-6">您目前的支出比實際支出<span class="gradient-text">損失更多</span></h2>
<p class="text-gray-300 max-w-2xl mx-auto text-lg">了解 PeakFlow 如何幫助您識別和解決行銷活動中的低效問題</p>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
<!-- PeakFlow 之前 -->
<div class="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50">
<div class="flex items-center justify-between mb-8">
<h3 class="text-2xl font-bold text-blue-400">PeakFlow 之前</h3>
<i class="ri-emotion-sad-line text-blue-400 text-2xl"></i>
</div>

<div class="space-y-6">
<div class="flex items-center justify-between">
<span class="text-gray-300">廣告支出效率</span>
<span class="text-red-400 font-bold">45%</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full" style="width: 45%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-gray-300">廣告活動投資報酬率</span>
<span class="text-red-400 font-bold">2.1倍</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full" style="width: 35%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-gray-300">轉換率</span>
<span class="text-red-400 font-bold">3.2%</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full" style="width: 32%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-gray-300">客戶獲取成本</span>
<span class="text-red-400 font-bold">142美元</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full" style="width: 25%"></div>
</div>
</div>

<button class="w-full mt-8 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-500/30 text-blue-400 py-3 rounded-lg transition-all duration-300">
了解更多
</button>
</div>

<!-- 峰值流量之後 -->
<div class="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50">
<div class="flex items-center justify-between mb-8">
<h3 class="text-2xl font-bold text-green-400">峰值流量之後</h3>
<i class="ri-emotion-happy-line text-green-400 text-2xl"></i>
</div>

<div class="space-y-6">
<div class="flex items-center justify-between">
<span class="text-gray-300">廣告支出效率</span>
<span class="text-green-400 font-bold">85%</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full" style="width: 85%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-gray-300">廣告活動投資報酬率</span>
<span class="text-green-400 font-bold">4.7倍</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full" style="width: 80%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-gray-300">轉換率</span>
<span class="text-green-400 font-bold">7.8%</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full" style="width: 78%"></div>
</div>

<div class="flex items-center justify-between">
<span class="text-gray-300">客戶獲取成本</span>
<span class="text-green-400 font-bold">68美元</span>
</div>
<div class="w-full bg-gray-700 rounded-full h-3">
<div class="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full" style="width: 75%"></div>
</div>
</div>

<button class="w-full mt-8 bg-green-600/20 hover:bg-green-600/30 border border-green-500/30 text-green-400 py-3 rounded-lg transition-all duration-300">
了解更多
</button>
</div>
</div>
</div>

</div>
</section>
<script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>
<!-- Pricing Section -->
<!-- FAQ Section -->
<section class="py-20 px-6 bg-black" id="sec5">
<div class="max-w-4xl mx-auto">
<div class="text-center mb-16">
<h2 class="text-primary text-xl mb-3">常見問題</h2>
<h3 class="text-4xl font-bold mb-6">您最關心的問題解答</h3>
<p class="text-gray-300">深入了解 InfiniFlow 如何為您的行銷活動帶來革命性改變</p>
</div>
<div class="space-y-4" id="faq-container">
<!-- FAQ Item 1 -->
<div class="border border-gray-800 rounded-xl overflow-hidden bg-gray-900/30 backdrop-blur-sm">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none hover:bg-gray-800/50 transition-all duration-300">
<span class="text-lg font-medium flex items-center">
<span class="mr-3">🚀</span>
InfiniFlow 如何讓我的行銷活動脫胎換骨？
</span>
<i class="ri-add-line ri-lg faq-icon text-primary"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300 leading-relaxed">
InfiniFlow 搭載頂尖 AI 智慧引擎，深度解析您的全通路行銷數據，精準定位每一分預算的流向。我們的系統能即時發現隱藏的效率漏洞、無效支出，並主動提供數據驅動的優化建議。更棒的是，平台會自動執行最佳化策略，讓您在零增預算的前提下，實現 ROI 的顯著提升。
</p>
</div>
</div>
<!-- FAQ Item 2 -->
<div class="border border-gray-800 rounded-xl overflow-hidden bg-gray-900/30 backdrop-blur-sm">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none hover:bg-gray-800/50 transition-all duration-300">
<span class="text-lg font-medium flex items-center">
<span class="mr-3">🔗</span>
InfiniFlow 支援哪些主流行銷平台？
</span>
<i class="ri-add-line ri-lg faq-icon text-primary"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300 leading-relaxed">
我們與業界所有頂級平台無縫整合，涵蓋 Google Ads、Meta 廣告系列（Facebook & Instagram）、LinkedIn、X（前Twitter）、TikTok、Pinterest、Amazon Advertising 等主流渠道。針對企業級客戶，我們更提供專屬 API 客製化整合服務，確保您的獨特行銷生態系統完美運作。
</p>
</div>
</div>
<!-- FAQ Item 3 -->
<div class="border border-gray-800 rounded-xl overflow-hidden bg-gray-900/30 backdrop-blur-sm">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none hover:bg-gray-800/50 transition-all duration-300">
<span class="text-lg font-medium flex items-center">
<span class="mr-3">⏰</span>
多久能看見實際成效？
</span>
<i class="ri-add-line ri-lg faq-icon text-primary"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300 leading-relaxed">
根據我們的客戶數據統計，<strong class="text-green-400">85% 的用戶</strong>在導入後 <strong class="text-blue-400">2-4 週內</strong> 就能感受到明顯的效果提升。而 AI 演算法的深度學習優化通常在 <strong class="text-purple-400">60-90 天</strong> 達到最佳狀態，此時系統已完全適應您的業務特性和目標受眾，帶來持續且穩定的成長動能。
</p>
</div>
</div>
<!-- FAQ Item 4 -->
<div class="border border-gray-800 rounded-xl overflow-hidden bg-gray-900/30 backdrop-blur-sm">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none hover:bg-gray-800/50 transition-all duration-300">
<span class="text-lg font-medium flex items-center">
<span class="mr-3">📋</span>
需要簽訂長期合約嗎？
</span>
<i class="ri-add-line ri-lg faq-icon text-primary"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<p class="text-gray-300 leading-relaxed">
<strong class="text-green-400">完全不需要！</strong>InfiniFlow 採用彈性的月付制訂閱模式，讓您享有最大的使用自由度。當然，我們也提供年度訂閱的優惠方案。您可以隨時調整方案等級或取消服務，不過我們建議至少體驗 <strong class="text-blue-400">3 個月</strong>，讓 AI 有充分時間學習並展現其強大的優化潛力。
</p>
</div>
</div>
<!-- FAQ Item 5 -->
<div class="border border-gray-800 rounded-xl overflow-hidden bg-gray-900/30 backdrop-blur-sm">
<button class="faq-button w-full flex items-center justify-between p-6 text-left focus:outline-none hover:bg-gray-800/50 transition-all duration-300">
<span class="text-lg font-medium flex items-center">
<span class="mr-3">🎯</span>
提供企業級客製化服務嗎？
</span>
<i class="ri-add-line ri-lg faq-icon text-primary"></i>
</button>
<div class="faq-content hidden px-6 pb-6">
<div class="text-gray-300 leading-relaxed">
<p class="mb-4"><strong class="text-green-400">當然！</strong>我們的企業解決方案專為大型組織量身打造，包含：</p>
<ul class="space-y-2 ml-4">
<li class="flex items-start">
<span class="text-blue-400 mr-2">•</span>
<strong class="text-blue-400">專屬儀表板設計</strong> - 符合您品牌風格的客製化報告介面
</li>
<li class="flex items-start">
<span class="text-green-400 mr-2">•</span>
<strong class="text-green-400">深度系統整合</strong> - 與您現有的 MarTech 堆疊完美融合
</li>
<li class="flex items-start">
<span class="text-purple-400 mr-2">•</span>
<strong class="text-purple-400">專業客戶成功經理</strong> - 一對一策略諮詢與技術支援
</li>
<li class="flex items-start">
<span class="text-pink-400 mr-2">•</span>
<strong class="text-pink-400">個人化 AI 策略</strong> - 基於您獨特商業模式的專屬優化演算法
</li>
</ul>
</div>
</div>
</div>
</div>
</div>
</section>
<script id="stats-animation">
document.addEventListener('DOMContentLoaded', function() {
const stats = document.querySelectorAll('.stat-number');
function animateNumber(element, target) {
let current = 0;
const increment = target / 50;
const duration = 2000;
const interval = duration / 50;
const timer = setInterval(() => {
current += increment;
if (current >= target) {
current = target;
clearInterval(timer);
}
element.textContent = Math.round(current);
}, interval);
}
const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const target = parseInt(entry.target.dataset.target);
animateNumber(entry.target, target);
observer.unobserve(entry.target);
}
});
}, { threshold: 0.5 });
stats.forEach(stat => observer.observe(stat));
});
</script>
<!-- CTA Section -->
<section class="relative h-[400px] overflow-hidden rounded-3xl mx-auto max-w-7xl">
<div class="absolute inset-0 bg-gradient-to-r from-pink-300 via-purple-300 to-blue-300"></div>
<style>
@keyframes rotateClockwise {
0% {
transform: rotate(0deg);
}
100% {
transform: rotate(360deg);
}
}
@keyframes rotateCounterClockwise {
0% {
transform: rotate(0deg);
}
100% {
transform: rotate(-360deg);
}
}
</style>
<div class="absolute left-[10%] top-1/2 -translate-y-1/2 w-[40%] perspective-[1000px] flex items-center gap-4">
<div class="w-2/3 relative z-0">
<img src="./assets/98396f56-37d0-4c87-ace9-f7de22e11642.jpg" alt="Abstract Shape" class="w-full h-auto object-cover scale-150" style="animation: rotateClockwise 20s linear infinite;">
</div>
<div class="w-1/2 relative z-10 -ml-8">
<img src="./assets/3cf1d9a4-5823-403b-b9da-7dcf837b7aea.jpg" alt="Abstract Shape 2" class="w-full h-auto object-cover" style="animation: rotateCounterClockwise 20s linear infinite;">
</div>
</div>
<div class="absolute left-[55%] top-1/2 -translate-y-1/2 text-left">
<h2 class="text-5xl font-bold text-gray-900 mb-8">Unlock unlimited<br>growth.</h2>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-flex items-center bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-medium px-12 py-4 rounded-[100px] hover:opacity-90 transition-opacity whitespace-nowrap text-xl">
Get Started
</a>
</div>
</section>
<!-- Footer -->
<footer class="bg-gray-950 py-16 px-6">
<div class="max-w-7xl mx-auto">
<div class="grid grid-cols-1 md:grid-cols-5 gap-8 mb-12">
<div class="md:col-span-2">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="inline-block mb-6">
<span class="text-3xl font-[&#39;Pacifico&#39;] text-white">logo</span>
</a>
<p class="text-gray-400 mb-6 max-w-md">Powerful marketing automation platform that helps businesses grow through intelligent campaigns, analytics, and personalized customer experiences.</p>
<div class="flex space-x-4">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-twitter-x-line ri-lg"></i>
</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-linkedin-line ri-lg"></i>
</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-facebook-line ri-lg"></i>
</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary hover:text-white transition-colors">
<i class="ri-instagram-line ri-lg"></i>
</a>
</div>
</div>
<div>
<h5 class="font-bold mb-6">Product</h5>
<ul class="space-y-3">
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Features</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Pricing</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Integrations</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Updates</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Roadmap</a></li>
</ul>
</div>
<div>
<h5 class="font-bold mb-6">Company</h5>
<ul class="space-y-3">
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">About Us</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Careers</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Blog</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Press</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Contact</a></li>
</ul>
</div>
<div>
<h5 class="font-bold mb-6">Resources</h5>
<ul class="space-y-3">
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Documentation</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Help Center</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Community</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Webinars</a></li>
<li><a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-400 hover:text-primary transition-colors">Partners</a></li>
</ul>
</div>
</div>
<div class="pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
<p class="text-gray-500 mb-4 md:mb-0">© 2025 Marketing Platform. All rights reserved.</p>
<div class="flex flex-wrap gap-4">
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-500 hover:text-primary transition-colors">Terms of Service</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-500 hover:text-primary transition-colors">Privacy Policy</a>
<a href="https://readdy.link/share/a53c473bebb8998dc996de6634532094#" class="text-gray-500 hover:text-primary transition-colors">Cookie Policy</a>
</div>
</div>
</div>
</footer>
<!-- Chatbot Widget -->
<div id="chatbot-container" class="fixed bottom-10 right-6 z-50">
<!-- Chat Window -->
<div id="chat-window" class="hidden chat-window bg-gray-900 w-[360px] h-[480px] rounded-2xl shadow-2xl flex flex-col mb-4 border border-gray-800 origin-bottom-right">
<!-- Chat Header -->
<div class="p-4 border-b border-gray-800 flex items-center justify-between">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3">
<i class="ri-customer-service-2-line ri-lg text-primary"></i>
</div>
<div>
<h4 class="font-medium">Chat Support</h4>
<p class="text-sm text-gray-400">Online</p>
</div>
</div>
<button id="close-chat" class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-800 transition-colors">
<i class="ri-close-line ri-lg"></i>
</button>
</div>
<!-- Chat Messages -->
<div id="chat-messages" class="flex-1 overflow-y-auto p-4 space-y-4">
<div class="chat-message-bot flex items-start">
<div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3">
<i class="ri-customer-service-2-line text-primary"></i>
</div>
<div class="bg-gray-800 rounded-lg rounded-tl-none p-3 max-w-[80%]">
<p class="text-sm">Hello! 👋 How can I help you today?</p>
</div>
</div>
</div>
<!-- Chat Input -->
<div class="p-4 border-t border-gray-800">
<div class="flex items-center gap-2">
<input type="text" id="chat-input" placeholder="Type your message..." class="flex-1 bg-gray-800 rounded-full px-4 py-2 text-sm focus:ring-2 focus:ring-primary/50 border-none">
<button id="send-message" class="w-10 h-10 rounded-full bg-primary flex items-center justify-center hover:bg-primary/90 transition-colors">
<i class="ri-send-plane-fill ri-sm"></i>
</button>
</div>
</div>
</div>
<!-- Chatbot Button -->
<button id="chatbot-button" class="chatbot-widget w-16 h-16 rounded-full bg-primary flex items-center justify-center shadow-lg hover:shadow-xl">
<i class="ri-customer-service-2-line ri-2x text-white"></i>
</button>
</div>
<script src="./assets/echarts.min.js"></script>
<script id="chart-script">
document.addEventListener('DOMContentLoaded', function() {
const chartContainer = document.getElementById('analytics-chart');
if (chartContainer) {
const chart = echarts.init(chartContainer);
const option = {
animation: false,
tooltip: {
trigger: 'axis',
backgroundColor: 'rgba(255, 255, 255, 0.8)',
textStyle: {
color: '#1f2937'
}
},
grid: {
top: 10,
right: 10,
bottom: 20,
left: 40
},
xAxis: {
type: 'category',
data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
axisLine: {
lineStyle: {
color: '#4b5563'
}
},
axisLabel: {
color: '#9ca3af'
}
},
yAxis: {
type: 'value',
axisLine: {
lineStyle: {
color: '#4b5563'
}
},
splitLine: {
lineStyle: {
color: '#374151'
}
},
axisLabel: {
color: '#9ca3af'
}
},
series: [
{
name: 'Engagement',
type: 'line',
smooth: true,
data: [120, 132, 101, 134, 90, 180],
lineStyle: {
color: 'rgba(87, 181, 231, 1)'
},
itemStyle: {
color: 'rgba(87, 181, 231, 1)'
},
showSymbol: false,
areaStyle: {
color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
{
offset: 0,
color: 'rgba(87, 181, 231, 0.3)'
},
{
offset: 1,
color: 'rgba(87, 181, 231, 0.1)'
}
])
}
},
{
name: 'Conversions',
type: 'line',
smooth: true,
data: [220, 182, 191, 234, 290, 330],
lineStyle: {
color: 'rgba(141, 211, 199, 1)'
},
itemStyle: {
color: 'rgba(141, 211, 199, 1)'
},
showSymbol: false,
areaStyle: {
color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
{
offset: 0,
color: 'rgba(141, 211, 199, 0.3)'
},
{
offset: 1,
color: 'rgba(141, 211, 199, 0.1)'
}
])
}
}
]
};
chart.setOption(option);
window.addEventListener('resize', function() {
chart.resize();
});
}
});
</script>
<script id="faq-script">
document.addEventListener('DOMContentLoaded', function() {
const faqButtons = document.querySelectorAll('.faq-button');
faqButtons.forEach(button => {
button.addEventListener('click', function() {
const content = this.nextElementSibling;
const icon = this.querySelector('.faq-icon');
if (content.classList.contains('hidden')) {
content.classList.remove('hidden');
icon.classList.remove('ri-add-line');
icon.classList.add('ri-subtract-line');
} else {
content.classList.add('hidden');
icon.classList.remove('ri-subtract-line');
icon.classList.add('ri-add-line');
}
});
});
});
</script>

<!-- 頁籤切換功能 -->
<script id="tab-switch-script">
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const contentArea = document.getElementById('sec3');

    // 產業內容數據
    const industryData = {
        ecommerce1: {
            title: '電商節慶活動',
            subtitle: '智慧驅動，營收倍增',
            icon: 'ri-shopping-cart-line',
            scenarios: [
                {
                    title: '全自動「節慶活動」生成器',
                    painPoint: '為了母親節、雙11，行銷團隊提前一個月加班準備活動頁。',
                    solution: '只需在表單輸入「活動主題：母親節」、「主打商品：保養品」，系統自動生成充滿節日氛圍的登陸頁、EDM、社群貼文素材。',
                    benefit: '行銷活動準備時間縮短90%，讓團隊能同時操作多個活動，抓住每個商機。'
                }
            ],
            image: './assets/search-image'
        },
        ecommerce2: {
            title: '電商商品頁面',
            subtitle: '千人千面，精準轉換',
            icon: 'ri-store-line',
            scenarios: [
                {
                    title: '千人千面的「商品故事頁」',
                    painPoint: '所有商品共用一個呆板的介紹模板。',
                    solution: '當您在後台新增商品時，AI會根據商品屬性自動生成獨一無二的銷售文案、使用情境和品牌故事，並填入專屬頁面。',
                    benefit: '提升商品頁轉換率25%+，創造更強的品牌情感連結。'
                }
            ],
            image: './assets/search-image'
        },
        realestate: {
            title: '房地產與仲介業',
            subtitle: '專業呈現，加速成交',
            icon: 'ri-building-line',
            scenarios: [
                {
                    title: '每個物件的「專屬金牌銷售網站」',
                    painPoint: '物件資訊僅僅是幾張照片和制式化的文字列表。',
                    solution: '仲介在後台App上傳新建案的照片和基本資料，InfiniFlow™ 自動為該物件生成一個獨立的精美介紹網站，包含AI生成的周邊生活圈分析、格局優點描述等。',
                    benefit: '提升物件的專業感與吸引力，增加約看率40%+。'
                }
            ],
            image: './assets/search-image(1)'
        },
        education: {
            title: '教育、顧問與知識付費產業',
            subtitle: '知識變現，毫不費力',
            icon: 'ri-book-line',
            scenarios: [
                {
                    title: '源源不絕的「課程與講座」銷售頁',
                    painPoint: '講師想開新課，但被製作銷售頁面的技術問題卡住。',
                    solution: '講師只需填寫一份課程大綱問卷，系統自動生成具備高度說服力的課程銷售頁。',
                    benefit: '新課程上架速度提升5倍，讓專家能專注於內容開發。'
                }
            ],
            image: './assets/search-image(2)'
        },
        saas: {
            title: 'SaaS與軟體服務業',
            subtitle: '資訊同步，價值加速',
            icon: 'ri-cloud-line',
            scenarios: [
                {
                    title: '新功能發布的「即時說明文件與案例」',
                    painPoint: '產品更新後，說明文件(Docs)和行銷部落格(Blog)總是不同步。',
                    solution: '開發團隊在Jira中完成一個新功能的註記，系統自動抓取資訊，生成給使用者的說明文件和給潛在客戶看的應用案例。',
                    benefit: '加速產品價值傳遞，確保行銷內容與產品功能永遠同步。'
                }
            ],
            image: './assets/search-image(3)'
        },
        beauty1: {
            title: '醫美見證案例',
            subtitle: '創造渴望，加速預約',
            icon: 'ri-heart-pulse-line',
            scenarios: [
                {
                    title: '「見證案例」自動生成與發布系統',
                    painPoint: '累積了大量成功的素人改造案例，但行銷人員沒時間一一撰寫成有吸引力的分享文，導致最有力的行銷資產被浪費。',
                    solution: '您的顧問只需在平板上傳客戶的「前後對比照」，並用勾選方式標記療程（如：皮秒雷射、玻尿酸填充）和客戶主要改善（如：膚色不均、法令紋）。InfiniFlow™ 會自動生成一篇符合醫療廣告法規、語氣專業又溫暖的「見證故事」網頁，並同步發布到官網和預約平台。',
                    benefit: '新案例上架時間縮短95%，讓官網永遠充滿最新、最真實的成功案例，大幅提升線上諮詢預約率達50%+。'
                }
            ],
            image: './assets/search-image(4)'
        },
        beauty2: {
            title: '醫美智慧推薦',
            subtitle: '個人化方案，精準成交',
            icon: 'ri-user-heart-line',
            scenarios: [
                {
                    title: '個人化「美麗方案」智慧推薦引擎',
                    painPoint: '客戶對療程一知半解，諮詢師需要花大量時間重複解說基礎知識。',
                    solution: '在官網設置一個「我的美麗煩惱」互動問卷（勾選：毛孔粗大、鬆弛、想瘦小腹）。客戶完成後，InfiniFlow™ 會即時生成一個專屬的「個人化建議方案」頁面，結合AI生成的衛教知識、推薦療程組合、預算範圍和相關案例連結。',
                    benefit: '篩選出高意向客戶，有效縮短諮詢師溝通時間30%，讓客戶在到店前就已建立信任感與基本認知，提升當日成交率。'
                }
            ],
            image: './assets/search-image(4)'
        },
        creator1: {
            title: '創作者內容中樞',
            subtitle: '內容放大，專注創作',
            icon: 'ri-video-line',
            scenarios: [
                {
                    title: '個人品牌「內容中樞」自動化',
                    painPoint: '創作者在 YouTube、Instagram、TikTok 各平台發布內容，但缺乏一個統一的、能將流量變現的「個人官網」。手動更新所有平台的內容到官網上，費時費力。',
                    solution: '創作者只需專注在自己最擅長的平台（如YouTube）發布內容。InfiniFlow™ 會自動監測，一旦有新影片，立刻：1) 將影片嵌入官網；2) 將影片內容轉為SEO文章；3) 擷取精華片段製作成短影音發布到其他平台；4) 自動更新官網上的「最新作品」列表。',
                    benefit: '實現「一次創作，全網自動分發」，輕鬆打造專業的個人品牌網站，將粉絲沉澱為自己的流量資產，為開設線上課程、電商帶貨鋪路。'
                }
            ],
            image: './assets/search-image(5)'
        },
        creator2: {
            title: '創作者付費內容',
            subtitle: '知識變現，被動收入',
            icon: 'ri-money-dollar-circle-line',
            scenarios: [
                {
                    title: '粉絲贊助與付費內容「無人商店」',
                    painPoint: '想推出付費電子報、限定圖文或教學影片，但被建立會員系統、金流、內容發送等繁瑣技術流程卡住。',
                    solution: '創作者在一個簡單的表單中上傳他的付費內容（例如一份PDF或一段影片連結）和定價。InfiniFlow™ 自動為這個產品生成一個精美的銷售頁，並處理好金流串接。當有粉絲購買後，系統會自動將內容透過Email發送給粉絲。',
                    benefit: '讓任何創作者都能在10分鐘內擁有自己的付費產品，實現知識變現，創造被動收入，無需任何技術背景。'
                }
            ],
            image: './assets/search-image(5)'
        }
    };

    // 生成內容的函數
    function generateContent(industry) {
        const data = industryData[industry];
        if (!data) return '';

        // 只取第一個情境（因為現在每個分類只有一個情境）
        const scenario = data.scenarios[0];

        const scenarioHtml = `
            <!-- 場景標題 -->
            <div class="mb-8">
                <h5 class="text-3xl font-bold text-white mb-2">${scenario.title}</h5>
                <div class="w-20 h-1 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"></div>
            </div>

            <!-- 三階段展示 -->
            <div class="space-y-6">
                <!-- 痛點 -->
                <div class="group/card relative overflow-hidden bg-gradient-to-br from-red-900/20 to-red-800/10 border border-red-500/30 rounded-2xl p-6 hover:border-red-400/50 transition-all duration-300">
                    <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-transparent opacity-0 group-hover/card:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative flex items-start">
                        <div class="w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                            <i class="ri-error-warning-line text-red-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h6 class="font-bold text-red-400 mb-3 text-lg">🚫 傳統痛點</h6>
                            <p class="text-gray-300 leading-relaxed text-base">${scenario.painPoint}</p>
                        </div>
                    </div>
                </div>

                <!-- 解決方案 -->
                <div class="group/card relative overflow-hidden bg-gradient-to-br from-blue-900/20 to-blue-800/10 border border-blue-500/30 rounded-2xl p-6 hover:border-blue-400/50 transition-all duration-300">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-transparent opacity-0 group-hover/card:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative flex items-start">
                        <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                            <i class="ri-lightbulb-line text-blue-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h6 class="font-bold text-blue-400 mb-3 text-lg">💡 InfiniFlow™ 智慧解決</h6>
                            <p class="text-gray-300 leading-relaxed text-base">${scenario.solution}</p>
                        </div>
                    </div>
                </div>

                <!-- 效益 -->
                <div class="group/card relative overflow-hidden bg-gradient-to-br from-green-900/20 to-green-800/10 border border-green-500/30 rounded-2xl p-6 hover:border-green-400/50 transition-all duration-300">
                    <div class="absolute inset-0 bg-gradient-to-r from-green-500/5 to-transparent opacity-0 group-hover/card:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative flex items-start">
                        <div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                            <i class="ri-trophy-line text-green-400 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h6 class="font-bold text-green-400 mb-3 text-lg">🚀 實現效益</h6>
                            <p class="text-gray-300 leading-relaxed text-base">${scenario.benefit}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return `
            <div class="relative mt-16">
                <!-- 背景裝飾 -->
                <div class="absolute inset-0 bg-gradient-to-br from-gray-900/50 to-gray-800/30 rounded-3xl blur-3xl"></div>

                <div class="relative bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-gray-700/50 overflow-hidden">
                    <!-- 頂部光效 -->
                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent"></div>

                    <div class="grid grid-cols-1 xl:grid-cols-2 gap-12 items-stretch">
                        <!-- 左邊內容區域 -->
                        <div class="flex flex-col">
                            <!-- 產業標題 -->
                            <div class="text-center xl:text-left mb-8">
                                <div class="inline-flex items-center bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-2xl p-6">
                                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center mr-6">
                                        <i class="${data.icon} text-3xl text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-3xl font-bold text-white mb-2">${data.title}</h4>
                                        <p class="text-xl font-medium bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">${data.subtitle}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 場景內容 -->
                            <div class="flex-1">
                                ${scenarioHtml}
                            </div>

                            <!-- 行動呼籲 -->
                            <div class="text-center xl:text-left pt-8">
                                <div class="inline-flex items-center bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500 rounded-2xl px-8 py-4 text-white font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-purple-500/25 cursor-pointer">
                                    <i class="ri-rocket-line mr-3 text-lg"></i>
                                    <span>立即體驗 ${data.title} 解決方案</span>
                                    <i class="ri-arrow-right-line ml-3 text-lg"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 右邊圖片區域 -->
                        <div class="flex items-center justify-center xl:justify-end">
                            <div class="relative group w-full max-w-lg">
                                <div class="absolute -inset-4 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                <div class="relative bg-gray-800/50 rounded-2xl p-4 border border-gray-600/30 h-full flex items-center">
                                    <img src="${data.image}" alt="${data.title}" class="w-full h-auto rounded-xl shadow-2xl">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 切換頁籤函數
    function switchTab(targetTab) {
        // 更新按鈕狀態
        tabButtons.forEach(btn => {
            if (btn.dataset.tab === targetTab) {
                // 激活狀態
                btn.classList.add('active');
                btn.classList.remove('bg-gray-800/50', 'text-gray-300', 'hover:bg-gradient-to-r', 'hover:from-purple-600/80', 'hover:to-blue-600/80', 'border-gray-600/30', 'hover:border-purple-500/50');
                btn.classList.add('bg-gradient-to-r', 'from-purple-600', 'to-blue-600', 'text-white', 'shadow-lg', 'shadow-purple-500/25');

                // 更新圖標背景
                const icon = btn.querySelector('.w-8.h-8');
                if (icon) {
                    icon.classList.remove('bg-gray-700', 'group-hover:bg-white/20');
                    icon.classList.add('bg-white/20');
                }
            } else {
                // 非激活狀態
                btn.classList.remove('active');
                btn.classList.remove('bg-gradient-to-r', 'from-purple-600', 'to-blue-600', 'text-white', 'shadow-lg', 'shadow-purple-500/25');
                btn.classList.add('bg-gray-800/50', 'text-gray-300', 'hover:bg-gradient-to-r', 'hover:from-purple-600/80', 'hover:to-blue-600/80', 'border-gray-600/30', 'hover:border-purple-500/50');

                // 重置圖標背景
                const icon = btn.querySelector('.w-8.h-8');
                if (icon) {
                    icon.classList.remove('bg-white/20');
                    icon.classList.add('bg-gray-700', 'group-hover:bg-white/20');
                }
            }
        });

        // 更新內容
        contentArea.innerHTML = generateContent(targetTab);
    }

    // 綁定點擊事件
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            switchTab(targetTab);
        });
    });

    // 初始化顯示第一個頁籤
    switchTab('ecommerce1');
});
</script>

<!-- 聊天機器人 -->
<script id="chatbot-script">
document.addEventListener('DOMContentLoaded', function() {
const chatbotButton = document.getElementById('chatbot-button');
const chatWindow = document.getElementById('chat-window');
const closeChat = document.getElementById('close-chat');
const chatInput = document.getElementById('chat-input');
const sendMessage = document.getElementById('send-message');
const chatMessages = document.getElementById('chat-messages');
function addMessage(message, isUser = false) {
const messageDiv = document.createElement('div');
messageDiv.className = isUser ? 'chat-message-user flex items-start justify-end' : 'chat-message-bot flex items-start';
const messageContent = `
${isUser ? '' : `
<div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3">
<i class="ri-customer-service-2-line text-primary"></i>
</div>
`}
<div class="bg-${isUser ? 'primary' : 'gray-800'} rounded-lg ${isUser ? 'rounded-tr-none' : 'rounded-tl-none'} p-3 max-w-[80%]">
<p class="text-sm">${message}</p>
</div>
`;
messageDiv.innerHTML = messageContent;
chatMessages.appendChild(messageDiv);
chatMessages.scrollTop = chatMessages.scrollHeight;
}
chatbotButton.addEventListener('click', function() {
chatWindow.classList.remove('hidden');
chatbotButton.classList.add('hidden');
if (!chatWindow.classList.contains('hidden')) {
chatInput.focus();
}
});
closeChat.addEventListener('click', function() {
chatWindow.classList.add('hidden');
chatbotButton.classList.remove('hidden');
});
function handleSendMessage() {
const message = chatInput.value.trim();
if (message) {
addMessage(message, true);
chatInput.value = '';
// Simulate bot response
setTimeout(() => {
addMessage("Thanks for your message! Our team will get back to you soon.");
}, 1000);
}
}
sendMessage.addEventListener('click', handleSendMessage);
chatInput.addEventListener('keypress', function(e) {
if (e.key === 'Enter') {
handleSendMessage();
}
});
});

// 導航功能
document.addEventListener('DOMContentLoaded', function() {
    // 平滑滾動到指定區塊
    function smoothScrollTo(targetId) {
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
            const offsetTop = targetElement.offsetTop - 80; // 考慮導航欄高度
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    }

    // 桌面版導航連結點擊事件
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            smoothScrollTo(targetId);
        });
    });

    // 手機版選單功能
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuClose = document.getElementById('mobile-menu-close');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

    // 開啟手機版選單
    mobileMenuBtn.addEventListener('click', function() {
        mobileMenu.classList.remove('-translate-x-full');
        mobileMenu.classList.add('translate-x-0');
        document.body.style.overflow = 'hidden'; // 防止背景滾動
    });

    // 關閉手機版選單
    function closeMobileMenu() {
        mobileMenu.classList.remove('translate-x-0');
        mobileMenu.classList.add('-translate-x-full');
        document.body.style.overflow = ''; // 恢復滾動
    }

    mobileMenuClose.addEventListener('click', closeMobileMenu);

    // 點擊選單背景關閉
    mobileMenu.addEventListener('click', function(e) {
        if (e.target === mobileMenu) {
            closeMobileMenu();
        }
    });

    // 手機版導航連結點擊事件
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            smoothScrollTo(targetId);
            closeMobileMenu(); // 點擊後關閉選單
        });
    });

    // Logo 點擊事件
    const logoLinks = document.querySelectorAll('a[href="#sec1"]');
    logoLinks.forEach(link => {
        if (link.querySelector('span')) { // 確保是 logo 連結
            link.addEventListener('click', function(e) {
                e.preventDefault();
                smoothScrollTo('#sec1');
                if (mobileMenu.classList.contains('translate-x-0')) {
                    closeMobileMenu();
                }
            });
        }
    });

    // 監聽滾動事件，高亮當前區塊對應的導航項目
    function updateActiveNavItem() {
        const sections = ['#sec1', '#sec2', '#use-cases-sec', '#sec4', '#sec5'];
        const scrollPosition = window.scrollY + 100;

        sections.forEach((sectionId, index) => {
            const section = document.querySelector(sectionId);
            if (section) {
                const sectionTop = section.offsetTop;
                const sectionBottom = sectionTop + section.offsetHeight;

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    // 移除所有活動狀態
                    navLinks.forEach(link => link.classList.remove('text-primary'));
                    mobileNavLinks.forEach(link => link.classList.remove('text-primary'));

                    // 添加當前區塊的活動狀態
                    const activeNavLink = document.querySelector(`.nav-link[href="${sectionId}"]`);
                    const activeMobileNavLink = document.querySelector(`.mobile-nav-link[href="${sectionId}"]`);

                    if (activeNavLink) activeNavLink.classList.add('text-primary');
                    if (activeMobileNavLink) activeMobileNavLink.classList.add('text-primary');
                }
            }
        });
    }

    // 節流函數，優化滾動性能
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(updateActiveNavItem, 10);
    });

    // 初始化時設定活動狀態
    updateActiveNavItem();
});
</script>

</body></html>